# Task 06: Real-time Updates System

## Objective
Implement real-time data synchronization across the admin dashboard using PocketBase subscriptions to provide live updates for user activities, system events, and platform metrics without requiring manual refresh.

## Dependencies
- Task 01: Admin Authentication Setup
- Task 02: Basic Dashboard Structure
- Task 04: Audit Logging System
- Task 05: Dashboard Overview Cards

## Estimated Time
2-3 days

## Deliverables

### 1. Real-time Update Service
**File**: `lib/src/features/admin_portal/data/services/realtime_update_service.dart`

**Requirements**:
- PocketBase subscription management
- Event filtering and routing
- Connection state monitoring
- Automatic reconnection handling
- Integration with existing notification patterns

### 2. Real-time Update Provider
**File**: `lib/src/features/admin_portal/application/providers/realtime_update_provider.dart`

**Requirements**:
- State management for real-time connections
- Event broadcasting to relevant providers
- Connection status tracking
- Error handling and retry logic

### 3. Live Activity Feed Widget
**File**: `lib/src/features/admin_portal/presentation/widgets/live_activity_feed_widget.dart`

**Requirements**:
- Real-time activity display
- Event categorization and filtering
- Auto-scrolling and pagination
- Responsive design implementation

### 4. Real-time Notification System
**File**: `lib/src/features/admin_portal/presentation/widgets/realtime_notification_widget.dart`

**Requirements**:
- Toast notifications for critical events
- System alert indicators
- User activity notifications
- Security event alerts

### 5. Connection Status Widget
**File**: `lib/src/features/admin_portal/presentation/widgets/connection_status_widget.dart`

**Requirements**:
- Real-time connection indicator
- Reconnection status display
- Error state visualization
- Manual reconnection controls

## Implementation Details

### Real-time Event Types
```dart
enum RealtimeEventType {
  userRegistration,
  userStatusChange,
  claimSubmission,
  claimStatusUpdate,
  systemAlert,
  securityEvent,
  adminAction,
  bulkOperation,
}
```

### Real-time Update Service Structure
```dart
class RealtimeUpdateService {
  final PocketBaseService _pocketBaseService;
  final Map<String, UnsubscribeFunc> _subscriptions = {};
  final StreamController<RealtimeEvent> _eventController = StreamController.broadcast();
  
  Stream<RealtimeEvent> get eventStream => _eventController.stream;
  
  Future<void> initialize();
  Future<void> subscribeToCollection(String collection);
  Future<void> unsubscribeFromCollection(String collection);
  void dispose();
}
```

### Real-time Event Model
```dart
class RealtimeEvent {
  final String id;
  final RealtimeEventType type;
  final String collection;
  final String action; // create, update, delete
  final Map<String, dynamic> data;
  final DateTime timestamp;
  final String? userId;
  final String? userName;
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] Real-time updates for all critical collections (users, funding_applications, notifications)
- [ ] Live activity feed showing recent platform events
- [ ] Automatic dashboard metric updates without refresh
- [ ] Real-time notifications for critical events
- [ ] Connection status monitoring and display
- [ ] Automatic reconnection on connection loss
- [ ] Event filtering and categorization

### Performance Requirements
- [ ] Real-time updates appear within 2 seconds
- [ ] Connection establishment within 5 seconds
- [ ] Minimal impact on dashboard performance
- [ ] Efficient memory usage for long-running connections

### Reliability Requirements
- [ ] Automatic reconnection on network issues
- [ ] Graceful degradation when real-time unavailable
- [ ] Error handling for subscription failures
- [ ] Connection state persistence across app lifecycle

### User Experience Requirements
- [ ] Clear visual indicators for real-time status
- [ ] Non-intrusive notifications for events
- [ ] Smooth updates without jarring UI changes
- [ ] Manual refresh option as fallback

## Code Examples

### Real-time Update Service Implementation
```dart
class RealtimeUpdateService {
  final PocketBaseService _pocketBaseService;
  final Map<String, UnsubscribeFunc> _subscriptions = {};
  final StreamController<RealtimeEvent> _eventController = StreamController.broadcast();
  bool _isInitialized = false;
  
  Stream<RealtimeEvent> get eventStream => _eventController.stream;
  
  Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      // Subscribe to critical collections
      await Future.wait([
        subscribeToCollection('users'),
        subscribeToCollection('funding_applications'),
        subscribeToCollection('notifications'),
        subscribeToCollection('admin_audit_logs'),
      ]);
      
      _isInitialized = true;
      LoggerService.info('Real-time update service initialized');
    } catch (e) {
      LoggerService.error('Failed to initialize real-time updates', e);
      rethrow;
    }
  }
  
  Future<void> subscribeToCollection(String collection) async {
    try {
      final unsubscribe = await _pocketBaseService.pb
          .collection(collection)
          .subscribe('*', (e) {
        final event = RealtimeEvent(
          id: e.record?.id ?? '',
          type: _getEventType(collection, e.action),
          collection: collection,
          action: e.action,
          data: e.record?.data ?? {},
          timestamp: DateTime.now(),
          userId: e.record?.data['user_id'],
          userName: e.record?.expand?['user_id']?['name'],
        );
        
        _eventController.add(event);
        LoggerService.info('Real-time event: ${event.type} in $collection');
      });
      
      _subscriptions[collection] = unsubscribe;
    } catch (e) {
      LoggerService.error('Failed to subscribe to $collection', e);
      rethrow;
    }
  }
  
  RealtimeEventType _getEventType(String collection, String action) {
    switch (collection) {
      case 'users':
        return action == 'create' 
            ? RealtimeEventType.userRegistration 
            : RealtimeEventType.userStatusChange;
      case 'funding_applications':
        return action == 'create'
            ? RealtimeEventType.claimSubmission
            : RealtimeEventType.claimStatusUpdate;
      case 'admin_audit_logs':
        return RealtimeEventType.adminAction;
      default:
        return RealtimeEventType.systemAlert;
    }
  }
}
```

### Real-time Update Provider
```dart
final realtimeUpdateProvider = StateNotifierProvider<RealtimeUpdateNotifier, RealtimeUpdateState>((ref) {
  return RealtimeUpdateNotifier(ref.read(pocketBaseServiceProvider));
});

class RealtimeUpdateNotifier extends StateNotifier<RealtimeUpdateState> {
  final PocketBaseService _pocketBaseService;
  late final RealtimeUpdateService _realtimeService;
  StreamSubscription<RealtimeEvent>? _eventSubscription;
  
  RealtimeUpdateNotifier(this._pocketBaseService) : super(const RealtimeUpdateState()) {
    _realtimeService = RealtimeUpdateService(_pocketBaseService);
    _initialize();
  }
  
  Future<void> _initialize() async {
    try {
      state = state.copyWith(connectionStatus: ConnectionStatus.connecting);
      
      await _realtimeService.initialize();
      
      _eventSubscription = _realtimeService.eventStream.listen(
        _handleRealtimeEvent,
        onError: _handleError,
      );
      
      state = state.copyWith(connectionStatus: ConnectionStatus.connected);
    } catch (e) {
      state = state.copyWith(
        connectionStatus: ConnectionStatus.error,
        lastError: e.toString(),
      );
    }
  }
  
  void _handleRealtimeEvent(RealtimeEvent event) {
    // Update relevant providers based on event type
    switch (event.type) {
      case RealtimeEventType.userRegistration:
      case RealtimeEventType.userStatusChange:
        // Trigger user management provider refresh
        break;
      case RealtimeEventType.claimSubmission:
      case RealtimeEventType.claimStatusUpdate:
        // Trigger dashboard statistics refresh
        break;
      case RealtimeEventType.adminAction:
        // Update audit log provider
        break;
    }
    
    // Add to recent activities
    final updatedActivities = [event, ...state.recentActivities].take(50).toList();
    state = state.copyWith(recentActivities: updatedActivities);
  }
}
```

### Live Activity Feed Widget
```dart
class LiveActivityFeedWidget extends ConsumerWidget {
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final realtimeState = ref.watch(realtimeUpdateProvider);
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      title: Row(
        children: [
          const Text('Live Activity'),
          const SizedBox(width: 8),
          ConnectionStatusIndicator(status: realtimeState.connectionStatus),
        ],
      ),
      child: SizedBox(
        height: 400,
        child: realtimeState.recentActivities.isEmpty
            ? const Center(child: Text('No recent activity'))
            : ListView.builder(
                itemCount: realtimeState.recentActivities.length,
                itemBuilder: (context, index) {
                  final activity = realtimeState.recentActivities[index];
                  return ActivityListTile(activity: activity);
                },
              ),
      ),
    );
  }
}

class ActivityListTile extends StatelessWidget {
  final RealtimeEvent activity;
  
  const ActivityListTile({required this.activity});
  
  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return ListTile(
      leading: _buildActivityIcon(),
      title: Text(_getActivityTitle()),
      subtitle: Text(_getActivityDescription()),
      trailing: Text(
        _formatTimestamp(activity.timestamp),
        style: theme.textTheme.small.copyWith(
          color: theme.colorScheme.mutedForeground,
        ),
      ),
    );
  }
  
  Widget _buildActivityIcon() {
    IconData icon;
    Color color;
    
    switch (activity.type) {
      case RealtimeEventType.userRegistration:
        icon = LucideIcons.userPlus;
        color = Colors.green;
        break;
      case RealtimeEventType.claimSubmission:
        icon = LucideIcons.fileText;
        color = Colors.blue;
        break;
      case RealtimeEventType.securityEvent:
        icon = LucideIcons.shield;
        color = Colors.red;
        break;
      default:
        icon = LucideIcons.activity;
        color = Colors.grey;
    }
    
    return Icon(icon, color: color, size: 20);
  }
}
```

### Connection Status Widget
```dart
class ConnectionStatusIndicator extends StatelessWidget {
  final ConnectionStatus status;
  
  const ConnectionStatusIndicator({required this.status});
  
  @override
  Widget build(BuildContext context) {
    Color color;
    IconData icon;
    String tooltip;
    
    switch (status) {
      case ConnectionStatus.connected:
        color = Colors.green;
        icon = LucideIcons.wifi;
        tooltip = 'Connected - Real-time updates active';
        break;
      case ConnectionStatus.connecting:
        color = Colors.orange;
        icon = LucideIcons.loader;
        tooltip = 'Connecting...';
        break;
      case ConnectionStatus.disconnected:
        color = Colors.grey;
        icon = LucideIcons.wifiOff;
        tooltip = 'Disconnected - Using cached data';
        break;
      case ConnectionStatus.error:
        color = Colors.red;
        icon = LucideIcons.alertCircle;
        tooltip = 'Connection error - Retrying...';
        break;
    }
    
    return Tooltip(
      message: tooltip,
      child: Icon(icon, color: color, size: 16),
    );
  }
}
```

## Testing Requirements

### Unit Tests
- Real-time service subscription management
- Event filtering and routing logic
- Connection state management
- Error handling scenarios

### Integration Tests
- End-to-end real-time update flow
- PocketBase subscription functionality
- Provider state updates
- UI component updates

### Performance Tests
- Memory usage with long-running connections
- Event processing performance
- UI update efficiency
- Connection recovery testing

## Error Handling

### Connection Failures
- Automatic retry with exponential backoff
- Graceful degradation to polling mode
- User notification of connection issues
- Manual reconnection option

### Event Processing Errors
- Individual event error isolation
- Logging of processing failures
- Continued operation despite errors
- Error reporting to admin

## Security Considerations

### Data Filtering
- Admin-only event access control
- Sensitive data masking in events
- Permission-based event filtering
- Audit logging for real-time access

### Connection Security
- Secure WebSocket connections
- Authentication token validation
- Rate limiting for event processing
- Protection against event flooding

## Next Steps
Upon completion, proceed to Task 07: Navigation and Routing to implement comprehensive navigation system for the admin portal.
