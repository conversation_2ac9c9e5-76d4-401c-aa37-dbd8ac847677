# Task 04: Audit Logging System

## Objective
Implement comprehensive audit logging system for all admin actions, extending existing user activity logging patterns to provide detailed tracking, compliance reporting, and security monitoring for administrative operations.

## Dependencies
- Task 01: Admin Authentication Setup
- Task 02: Basic Dashboard Structure
- Task 03: Admin User Management Interface

## Estimated Time
2-3 days

## Deliverables

### 1. Admin Audit Service
**File**: `lib/src/features/admin_portal/data/services/admin_audit_service.dart`

**Requirements**:
- Extend existing audit logging patterns
- Admin-specific action tracking
- Bulk operation logging
- Security event monitoring
- Integration with existing `user_activity_logs` collection

### 2. Audit Log Viewer Page
**File**: `lib/src/features/admin_portal/presentation/pages/audit_log_page.dart`

**Requirements**:
- Comprehensive audit trail display
- Advanced filtering and search capabilities
- Export functionality for compliance
- Real-time log updates
- Responsive design with detailed views

### 3. Audit Log Models
**File**: `lib/src/features/admin_portal/data/models/admin_audit_models.dart`

**Requirements**:
- Admin audit log entry model
- Filter and search criteria models
- Export format models
- Security event classification

### 4. Audit Log Provider
**File**: `lib/src/features/admin_portal/application/providers/audit_log_provider.dart`

**Requirements**:
- State management for audit logs
- Real-time updates via PocketBase subscriptions
- Filter and search state management
- Export operation handling

### 5. Audit Log Widgets
**File**: `lib/src/features/admin_portal/presentation/widgets/audit_log_widgets.dart`

**Requirements**:
- Audit log entry display components
- Filter and search widgets
- Export controls
- Security alert indicators

## Implementation Details

### Admin Audit Service Structure
```dart
class AdminAuditService extends PocketBaseService {
  Future<void> logAdminAction({
    required String action,
    required String adminId,
    String? targetUserId,
    String? entityType,
    String? entityId,
    Map<String, dynamic>? details,
    AdminActionSeverity severity = AdminActionSeverity.info,
  });
  
  Future<List<AdminAuditEntry>> getAuditLogs({
    String? adminId,
    String? action,
    DateTime? startDate,
    DateTime? endDate,
    AdminActionSeverity? severity,
    int page = 1,
    int perPage = 50,
  });
  
  Future<void> logBulkOperation({
    required String operation,
    required List<String> targetIds,
    required String adminId,
    Map<String, dynamic>? details,
  });
}
```

### Audit Action Categories
```dart
enum AdminActionType {
  userManagement,
  contentManagement,
  systemConfiguration,
  securityEvent,
  dataExport,
  bulkOperation,
}

enum AdminActionSeverity {
  info,
  warning,
  critical,
  security,
}
```

### Audit Log Entry Model
```dart
class AdminAuditEntry {
  final String id;
  final DateTime timestamp;
  final String adminId;
  final String adminName;
  final AdminActionType actionType;
  final String action;
  final AdminActionSeverity severity;
  final String? targetUserId;
  final String? targetUserName;
  final String? entityType;
  final String? entityId;
  final Map<String, dynamic> details;
  final String ipAddress;
  final String userAgent;
}
```

## Acceptance Criteria

### Functional Requirements
- [ ] All admin actions are automatically logged
- [ ] Audit logs include comprehensive context information
- [ ] Real-time audit log viewing with filtering
- [ ] Search functionality across all audit fields
- [ ] Export capabilities for compliance reporting
- [ ] Bulk operation tracking with individual item details
- [ ] Security event classification and alerting

### Performance Requirements
- [ ] Audit logging doesn't impact admin operation performance
- [ ] Audit log queries return results within 2 seconds
- [ ] Real-time updates appear within 5 seconds
- [ ] Export operations complete within 30 seconds

### Security Requirements
- [ ] Audit logs are tamper-proof and immutable
- [ ] Sensitive data is properly masked in logs
- [ ] Admin access to audit logs is properly controlled
- [ ] Security events trigger appropriate alerts

### Compliance Requirements
- [ ] All required fields captured for compliance
- [ ] Audit trail meets regulatory standards
- [ ] Data retention policies implemented
- [ ] Export formats support compliance reporting

## Code Examples

### Admin Audit Service Implementation
```dart
class AdminAuditService extends PocketBaseService {
  Future<void> logAdminAction({
    required String action,
    required String adminId,
    String? targetUserId,
    String? entityType,
    String? entityId,
    Map<String, dynamic>? details,
    AdminActionSeverity severity = AdminActionSeverity.info,
  }) async {
    try {
      final auditEntry = {
        'admin_id': adminId,
        'action_type': _getActionType(action).name,
        'action': action,
        'severity': severity.name,
        'target_user_id': targetUserId,
        'entity_type': entityType,
        'entity_id': entityId,
        'details': details ?? {},
        'ip_address': await _getCurrentIpAddress(),
        'user_agent': await _getUserAgent(),
        'timestamp': DateTime.now().toIso8601String(),
      };
      
      await createRecord(
        collectionName: 'admin_audit_logs',
        data: auditEntry,
      );
      
      // Trigger security alerts for critical actions
      if (severity == AdminActionSeverity.critical || 
          severity == AdminActionSeverity.security) {
        await _triggerSecurityAlert(auditEntry);
      }
      
    } catch (e) {
      LoggerService.error('Failed to log admin action', e);
      // Don't rethrow - audit logging failure shouldn't break admin operations
    }
  }
  
  Future<List<AdminAuditEntry>> getAuditLogs({
    String? adminId,
    String? action,
    DateTime? startDate,
    DateTime? endDate,
    AdminActionSeverity? severity,
    int page = 1,
    int perPage = 50,
  }) async {
    try {
      String filter = _buildAuditFilter(
        adminId: adminId,
        action: action,
        startDate: startDate,
        endDate: endDate,
        severity: severity,
      );
      
      final records = await getList(
        collectionName: 'admin_audit_logs',
        page: page,
        perPage: perPage,
        filter: filter,
        sort: '-created',
        expand: 'admin_id,target_user_id',
      );
      
      return records.items
          .map((record) => AdminAuditEntry.fromRecord(record))
          .toList();
    } catch (e) {
      LoggerService.error('Error fetching audit logs', e);
      rethrow;
    }
  }
}
```

### Audit Log Page Structure
```dart
class AuditLogPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/audit-logs';
  
  @override
  ConsumerState<AuditLogPage> createState() => _AuditLogPageState();
}

class _AuditLogPageState extends ConsumerState<AuditLogPage> {
  final TextEditingController _searchController = TextEditingController();
  AdminActionType? _selectedActionType;
  AdminActionSeverity? _selectedSeverity;
  DateTimeRange? _selectedDateRange;
  
  @override
  Widget build(BuildContext context) {
    final auditLogsAsync = ref.watch(auditLogProvider);
    
    return Scaffold(
      appBar: AppBar(
        title: const Text('Audit Logs'),
        actions: [
          IconButton(
            icon: const Icon(LucideIcons.download),
            onPressed: _exportAuditLogs,
          ),
          IconButton(
            icon: const Icon(LucideIcons.refreshCw),
            onPressed: () => ref.refresh(auditLogProvider),
          ),
        ],
      ),
      body: Column(
        children: [
          _buildFiltersSection(),
          Expanded(
            child: auditLogsAsync.when(
              data: (logs) => _buildAuditLogsList(logs),
              loading: () => const AuditLogLoadingWidget(),
              error: (error, stack) => AuditLogErrorWidget(error: error),
            ),
          ),
        ],
      ),
    );
  }
  
  Widget _buildAuditLogsList(List<AdminAuditEntry> logs) {
    return ListView.builder(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      itemCount: logs.length,
      itemBuilder: (context, index) {
        final log = logs[index];
        return AuditLogEntryWidget(
          entry: log,
          onTap: () => _showAuditLogDetails(log),
        );
      },
    );
  }
}
```

### Audit Log Entry Widget
```dart
class AuditLogEntryWidget extends StatelessWidget {
  final AdminAuditEntry entry;
  final VoidCallback? onTap;
  
  const AuditLogEntryWidget({
    required this.entry,
    this.onTap,
  });
  
  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      child: ListTile(
        leading: _buildSeverityIcon(),
        title: Text(
          entry.action,
          style: theme.textTheme.p.copyWith(fontWeight: FontWeight.w600),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Admin: ${entry.adminName}'),
            if (entry.targetUserName != null)
              Text('Target: ${entry.targetUserName}'),
            Text('Time: ${_formatTimestamp(entry.timestamp)}'),
          ],
        ),
        trailing: Icon(
          LucideIcons.chevronRight,
          color: theme.colorScheme.mutedForeground,
        ),
        onTap: onTap,
      ),
    );
  }
  
  Widget _buildSeverityIcon() {
    IconData icon;
    Color color;
    
    switch (entry.severity) {
      case AdminActionSeverity.info:
        icon = LucideIcons.info;
        color = Colors.blue;
        break;
      case AdminActionSeverity.warning:
        icon = LucideIcons.alertTriangle;
        color = Colors.orange;
        break;
      case AdminActionSeverity.critical:
        icon = LucideIcons.alertCircle;
        color = Colors.red;
        break;
      case AdminActionSeverity.security:
        icon = LucideIcons.shield;
        color = Colors.purple;
        break;
    }
    
    return Icon(icon, color: color);
  }
}
```

## Testing Requirements

### Unit Tests
- Audit service logging methods
- Filter and search logic
- Export functionality
- Security alert triggers

### Integration Tests
- End-to-end audit logging flow
- PocketBase integration validation
- Real-time update functionality
- Export format validation

### Security Tests
- Audit log immutability
- Access control verification
- Sensitive data masking
- Security alert functionality

## Data Schema

### Admin Audit Logs Collection
```json
{
  "name": "admin_audit_logs",
  "type": "base",
  "schema": [
    {"name": "admin_id", "type": "relation", "required": true},
    {"name": "action_type", "type": "select", "required": true},
    {"name": "action", "type": "text", "required": true},
    {"name": "severity", "type": "select", "required": true},
    {"name": "target_user_id", "type": "relation", "required": false},
    {"name": "entity_type", "type": "text", "required": false},
    {"name": "entity_id", "type": "text", "required": false},
    {"name": "details", "type": "json", "required": false},
    {"name": "ip_address", "type": "text", "required": false},
    {"name": "user_agent", "type": "text", "required": false}
  ]
}
```

## Integration Points

### Existing Services
- Extend existing audit logging patterns
- Integrate with PocketBaseService
- Use existing LoggerService for error handling
- Connect with notification service for alerts

### Admin Operations
- Automatically log all user management actions
- Track content management operations
- Monitor system configuration changes
- Record security-related events

## Next Steps
Upon completion, proceed to Task 05: Dashboard Overview Cards to implement the main dashboard KPI display.
