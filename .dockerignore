# Flutter/Dart build artifacts
.dart_tool/
build/
.packages
.flutter-plugins
.flutter-plugins-dependencies
*.g.dart
*.freezed.dart
*.mocks.dart

# Git and version control
.git/
.github/
.gitignore

# IDE files
.idea/
.vscode/
*.swp
*.swo
*~

# Project files
*.iml
three_pay_group_litigation_platform.iml
android/three_pay_group_litigation_platform_android.iml

# Logs
*.log
logs/

# Environment files (commented out .env to allow copying if needed)
# .env
.env.*
.env.local
.env.production
.env.staging

# Test files
test/
*_test.dart
coverage/

# Documentation
README.md
*.md
CHANGELOG.md
LICENSE

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Platform specific (not needed for web build)
android/
ios/
linux/
macos/
windows/

# Backend services (separate containers)
pocketbase/
webhook-service-ts/

# Development files
scripts/
tasks/
context7-mcp-server/
devtools_options.yaml
analysis_options.yaml

# Temporary files
tmp/
temp/
*.tmp
*.temp

# Legal and template files
nda_template.txt
legal_professionals_widget.dart
trodoo_collection.json
*.txt
*.pdf
