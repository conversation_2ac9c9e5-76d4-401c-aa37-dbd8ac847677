// lib/src/features/solicitor_portal/data/models/barrister.dart

// Helper method to parse relation fields that can be either a single string or a list
List<String> _parseRelationField(dynamic field) {
  if (field == null) return [];
  if (field is List) {
    return field.map((item) => item.toString()).toList();
  } else if (field is String) {
    return field.isEmpty ? [] : [field];
  }
  return [];
}

class Barrister {
  final String? id;
  final String barristerWithConduct;
  final String barristerChambers;
  final String email;
  final List<String>? claims;
  final double? performanceRating;
  final String? specialization;
  final String? phone;
  final String? notes;
  final double? successRate;
  final double? responseTimeAvg;
  final String? lastPerformanceReview;
  final List<String>? claimIds;
  final DateTime? created;
  final DateTime? updated;

  Barrister({
    this.id,
    required this.barristerWithConduct,
    required this.barristerChambers,
    required this.email,
    this.claims,
    this.performanceRating,
    this.specialization,
    this.phone,
    this.notes,
    this.successRate,
    this.responseTimeAvg,
    this.lastPerformanceReview,
    required this.claimIds,
    required this.created,
    required this.updated,
  });

  Barrister copyWith({
    String? id,
    String? barristerWithConduct,
    String? barristerChambers,
    String? email,
    List<String>? claims,
    double? performanceRating,
    String? specialization,
    String? phone,
    String? notes,
    double? successRate,
    double? responseTimeAvg,
    String? lastPerformanceReview,
    List<String>? claimIds,
    DateTime? created,
    DateTime? updated,
  }) {
    return Barrister(
      id: id ?? this.id,
      barristerWithConduct: barristerWithConduct ?? this.barristerWithConduct,
      barristerChambers: barristerChambers ?? this.barristerChambers,
      email: email ?? this.email,
      claims: claims ?? this.claims,
      performanceRating: performanceRating ?? this.performanceRating,
      specialization: specialization ?? this.specialization,
      phone: phone ?? this.phone,
      notes: notes ?? this.notes,
      successRate: successRate ?? this.successRate,
      responseTimeAvg: responseTimeAvg ?? this.responseTimeAvg,
      lastPerformanceReview:
          lastPerformanceReview ?? this.lastPerformanceReview,
      claimIds: claimIds ?? this.claimIds,
      created: created ?? this.created,
      updated: updated ?? this.updated,
    );
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = <String, dynamic>{};

    // DO NOT include 'id' in the JSON for create operations.
    // PocketBase generates it. For updates, it's in the URL.
    // if (id != null) {
    //   data['id'] = id;
    // }

    data['barrister_with_conduct'] = barristerWithConduct;
    data['barrister_chambers'] = barristerChambers;
    data['email'] = email;

    if (claims != null && claims!.isNotEmpty) {
      data['claims'] = claims;
    }

    if (performanceRating != null) {
      data['performance_rating'] = performanceRating;
    }

    if (specialization != null) {
      data['specialization'] = specialization;
    }

    if (phone != null) {
      data['phone'] = phone;
    }

    if (notes != null) {
      data['notes'] = notes;
    }

    if (successRate != null) {
      data['success_rate'] = successRate;
    }

    if (responseTimeAvg != null) {
      data['response_time_avg'] = responseTimeAvg;
    }

    if (lastPerformanceReview != null) {
      data['last_performance_review'] = lastPerformanceReview;
    }

    if (claimIds != null && claimIds!.isNotEmpty) {
      data['claim_ids'] = claimIds;
    }

    // Your 'created' and 'updated' fields are custom (not system:true)
    // and are also required in your Dart model's constructor.
    // If you are sending them during creation, this could be problematic
    // if PocketBase tries to interpret them as system fields or if they
    // are not in the exact format expected for a non-system date field.
    // For creation, it's generally best to let PocketBase handle timestamps
    // or ensure your backend schema is set up for client-provided dates
    // and that these are not conflicting with system expectations.
    // Given the "sql: no rows" error, let's try omitting them from the payload.
    // If your backend *requires* them, ensure they are correctly formatted.
    // if (created != null) {
    //   data['created'] = created!.toIso8601String();
    // }
    // if (updated != null) {
    //   data['updated'] = updated!.toIso8601String();
    // }

    return data;
  }

  factory Barrister.fromJson(Map<String, dynamic> json) {
    return Barrister(
      id: json['id'] as String?,
      barristerWithConduct: json['barrister_with_conduct'] as String,
      barristerChambers: json['barrister_chambers'] as String,
      email: json['email'] as String,
      claims: _parseRelationField(json['claims']),
      performanceRating: (json['performance_rating'] as num?)?.toDouble(),
      specialization: json['specialization'] as String?,
      phone: json['phone'] as String?,
      notes: json['notes'] as String?,
      successRate: (json['success_rate'] as num?)?.toDouble(),
      responseTimeAvg: (json['response_time_avg'] as num?)?.toDouble(),
      lastPerformanceReview: json['last_performance_review'] as String?,
      claimIds: _parseRelationField(json['claim_ids']),
      created:
          json['created'] != null ? DateTime.tryParse(json['created']) : null,
      updated:
          json['updated'] != null ? DateTime.tryParse(json['updated']) : null,
    );
  }

  @override
  String toString() {
    return barristerWithConduct;
  }
}
