import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/dashboard_statistics_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_dashboard_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/kpi_card_widgets.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// Dashboard overview widget with KPI cards and recent activity
class DashboardOverviewWidget extends ConsumerWidget {
  const DashboardOverviewWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final dashboardState = ref.watch(dashboardStatisticsProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    final isTablet = AdminResponsiveLayout.isTablet(context);

    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome header
          _buildWelcomeHeader(theme),
          const SizedBox(height: 24),
          
          // Error message
          if (dashboardState.error != null) ...[
            _buildErrorCard(theme, dashboardState.error!, ref),
            const SizedBox(height: 16),
          ],
          
          // KPI Cards Grid
          _buildKPICardsGrid(context, ref, isDesktop, isTablet, dashboardState),
          const SizedBox(height: 32),
          
          // Bottom section with recent activity and system health
          _buildBottomSection(context, ref, isDesktop, isTablet, dashboardState),
        ],
      ),
    );
  }

  Widget _buildWelcomeHeader(ShadThemeData theme) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Platform Overview',
          style: theme.textTheme.h2.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          'Monitor key metrics and platform performance in real-time.',
          style: theme.textTheme.muted,
        ),
      ],
    );
  }

  Widget _buildErrorCard(ShadThemeData theme, String error, WidgetRef ref) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              lucide.LucideIcons.alertCircle,
              color: theme.colorScheme.destructive,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                error,
                style: TextStyle(color: theme.colorScheme.destructive),
              ),
            ),
            ShadIconButton.ghost(
              icon: const Icon(lucide.LucideIcons.x, size: 16),
              onPressed: () {
                ref.read(dashboardStatisticsProvider.notifier).clearError();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildKPICardsGrid(
    BuildContext context,
    WidgetRef ref,
    bool isDesktop,
    bool isTablet,
    DashboardStatisticsState dashboardState,
  ) {
    final crossAxisCount = AdminResponsiveLayout.getCrossAxisCount(
      context,
      mobileCount: 2,
      tabletCount: 3,
      desktopCount: 4,
    );

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: AdminResponsiveLayout.getGridSpacing(context),
      mainAxisSpacing: AdminResponsiveLayout.getGridSpacing(context),
      childAspectRatio: isDesktop ? 1.2 : 1.0,
      children: [
        _buildUserKPICard(ref, dashboardState),
        _buildClaimsKPICard(ref, dashboardState),
        _buildFundingKPICard(ref, dashboardState),
        _buildRegistrationsKPICard(ref, dashboardState),
      ],
    );
  }

  Widget _buildUserKPICard(WidgetRef ref, DashboardStatisticsState dashboardState) {
    final notifier = ref.read(dashboardStatisticsProvider.notifier);
    
    return KPICard(
      title: 'Total Users',
      value: notifier.getKPIValue(DashboardKPI.totalUsers),
      subtitle: notifier.getKPISubtitle(DashboardKPI.totalUsers),
      icon: lucide.LucideIcons.users,
      iconColor: Colors.blue,
      trend: notifier.getKPITrend(DashboardKPI.totalUsers),
      isLoading: dashboardState.isLoading,
      onTap: () {
        ref.read(adminDashboardProvider.notifier).setSelectedNavItem(AdminNavigationItem.userManagement);
      },
    );
  }

  Widget _buildClaimsKPICard(WidgetRef ref, DashboardStatisticsState dashboardState) {
    final notifier = ref.read(dashboardStatisticsProvider.notifier);
    
    return KPICard(
      title: 'Active Claims',
      value: notifier.getKPIValue(DashboardKPI.activeClaims),
      subtitle: notifier.getKPISubtitle(DashboardKPI.activeClaims),
      icon: lucide.LucideIcons.fileText,
      iconColor: Colors.green,
      trend: notifier.getKPITrend(DashboardKPI.activeClaims),
      isLoading: dashboardState.isLoading,
      onTap: () {
        // TODO: Navigate to claims management when implemented
      },
    );
  }

  Widget _buildFundingKPICard(WidgetRef ref, DashboardStatisticsState dashboardState) {
    final notifier = ref.read(dashboardStatisticsProvider.notifier);
    
    return KPICard(
      title: 'Funding Volume',
      value: notifier.getKPIValue(DashboardKPI.fundingVolume),
      subtitle: notifier.getKPISubtitle(DashboardKPI.fundingVolume),
      icon: lucide.LucideIcons.trendingUp,
      iconColor: Colors.purple,
      trend: notifier.getKPITrend(DashboardKPI.fundingVolume),
      isLoading: dashboardState.isLoading,
      onTap: () {
        ref.read(adminDashboardProvider.notifier).setSelectedNavItem(AdminNavigationItem.analytics);
      },
    );
  }

  Widget _buildRegistrationsKPICard(WidgetRef ref, DashboardStatisticsState dashboardState) {
    final notifier = ref.read(dashboardStatisticsProvider.notifier);
    
    return KPICard(
      title: 'New Registrations',
      value: notifier.getKPIValue(DashboardKPI.newRegistrations),
      subtitle: notifier.getKPISubtitle(DashboardKPI.newRegistrations),
      icon: lucide.LucideIcons.userPlus,
      iconColor: Colors.orange,
      trend: notifier.getKPITrend(DashboardKPI.newRegistrations),
      isLoading: dashboardState.isLoading,
      onTap: () {
        ref.read(adminDashboardProvider.notifier).setSelectedNavItem(AdminNavigationItem.userManagement);
      },
    );
  }

  Widget _buildBottomSection(
    BuildContext context,
    WidgetRef ref,
    bool isDesktop,
    bool isTablet,
    DashboardStatisticsState dashboardState,
  ) {
    if (isDesktop) {
      return Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Activity (2/3 width)
          Expanded(
            flex: 2,
            child: _buildRecentActivitySection(ref, dashboardState),
          ),
          const SizedBox(width: 24),
          // System Health (1/3 width)
          Expanded(
            flex: 1,
            child: _buildSystemHealthSection(ref, dashboardState),
          ),
        ],
      );
    } else {
      return Column(
        children: [
          _buildSystemHealthSection(ref, dashboardState),
          const SizedBox(height: 24),
          _buildRecentActivitySection(ref, dashboardState),
        ],
      );
    }
  }

  Widget _buildRecentActivitySection(WidgetRef ref, DashboardStatisticsState dashboardState) {
    final recentActivities = ref.watch(recentActivitiesProvider);
    
    return RecentActivityCard(
      activities: recentActivities,
      isLoading: dashboardState.isLoading,
      onViewAll: () {
        ref.read(adminDashboardProvider.notifier).setSelectedNavItem(AdminNavigationItem.auditLogs);
      },
    );
  }

  Widget _buildSystemHealthSection(WidgetRef ref, DashboardStatisticsState dashboardState) {
    final systemHealth = ref.watch(systemHealthProvider);
    
    if (systemHealth == null) {
      return SystemHealthCard(
        systemHealth: SystemHealth(
          status: SystemHealthStatus.warning,
          description: 'Loading system health...',
          cpuUsage: 0.0,
          memoryUsage: 0.0,
          diskUsage: 0.0,
          activeConnections: 0,
          lastChecked: DateTime.now(),
        ),
        isLoading: dashboardState.isLoading,
        onTap: () {
          // TODO: Navigate to system health details when implemented
        },
      );
    }
    
    return SystemHealthCard(
      systemHealth: systemHealth,
      isLoading: dashboardState.isLoading,
      onTap: () {
        // TODO: Navigate to system health details when implemented
      },
    );
  }
}
