import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_auth_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_dashboard_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// Admin app bar widget with profile, notifications, and quick actions
class AdminAppBarWidget extends ConsumerWidget implements PreferredSizeWidget {
  const AdminAppBarWidget({super.key});

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final authState = ref.watch(adminAuthProvider);
    final dashboardState = ref.watch(adminDashboardProvider);
    final isDesktop = AdminResponsiveLayout.isDesktop(context);

    return AppBar(
      backgroundColor: theme.colorScheme.background,
      elevation: 1,
      surfaceTintColor: Colors.transparent,
      leading: isDesktop ? null : _buildMenuButton(context, ref, theme),
      title: _buildTitle(context, theme, dashboardState.selectedNavItem),
      actions: [
        _buildRefreshButton(context, ref, theme),
        const SizedBox(width: 8),
        _buildNotificationButton(context, ref, theme),
        const SizedBox(width: 8),
        _buildProfileMenu(context, ref, theme, authState),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget? _buildMenuButton(BuildContext context, WidgetRef ref, ShadThemeData theme) {
    return IconButton(
      icon: Icon(
        lucide.LucideIcons.menu,
        color: theme.colorScheme.foreground,
      ),
      onPressed: () {
        // TODO: Open mobile navigation drawer
        Scaffold.of(context).openDrawer();
      },
    );
  }

  Widget _buildTitle(BuildContext context, ShadThemeData theme, AdminNavigationItem selectedItem) {
    return Row(
      children: [
        Icon(
          _getNavItemIcon(selectedItem),
          size: 20,
          color: theme.colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Text(
          selectedItem.label,
          style: theme.textTheme.h4.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildRefreshButton(BuildContext context, WidgetRef ref, ShadThemeData theme) {
    final dashboardState = ref.watch(adminDashboardProvider);
    
    return ShadTooltip(
      builder: (context) => const Text('Refresh data'),
      child: ShadIconButton.ghost(
        icon: Icon(
          dashboardState.isLoading ? lucide.LucideIcons.loader2 : lucide.LucideIcons.refreshCw,
          size: 18,
        ),
        onPressed: dashboardState.isLoading ? null : () {
          ref.read(adminDashboardProvider.notifier).refreshStats();
          LoggerService.info('Admin dashboard refresh triggered');
        },
      ),
    );
  }

  Widget _buildNotificationButton(BuildContext context, WidgetRef ref, ShadThemeData theme) {
    final stats = ref.watch(dashboardStatsProvider);
    final unreadCount = stats?.unreadNotifications ?? 0;

    return ShadTooltip(
      builder: (context) => Text('Notifications${unreadCount > 0 ? ' ($unreadCount)' : ''}'),
      child: Stack(
        children: [
          ShadIconButton.ghost(
            icon: const Icon(
              lucide.LucideIcons.bell,
              size: 18,
            ),
            onPressed: () {
              // TODO: Navigate to notifications page
              LoggerService.info('Admin notifications button pressed');
            },
          ),
          if (unreadCount > 0)
            Positioned(
              right: 6,
              top: 6,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: theme.colorScheme.destructive,
                  borderRadius: BorderRadius.circular(8),
                ),
                constraints: const BoxConstraints(
                  minWidth: 16,
                  minHeight: 16,
                ),
                child: Text(
                  unreadCount > 99 ? '99+' : unreadCount.toString(),
                  style: TextStyle(
                    color: theme.colorScheme.destructiveForeground,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileMenu(BuildContext context, WidgetRef ref, ShadThemeData theme, AdminAuthState authState) {
    final adminUser = authState.adminUser;
    if (adminUser == null) return const SizedBox.shrink();

    return ShadPopover(
      popover: (context) => _buildProfilePopover(context, ref, theme, adminUser),
      child: ShadButton.ghost(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            CircleAvatar(
              radius: 16,
              backgroundColor: theme.colorScheme.primary,
              backgroundImage: adminUser.avatarUrl != null 
                  ? NetworkImage(adminUser.avatarUrl!) 
                  : null,
              child: adminUser.avatarUrl == null
                  ? Text(
                      adminUser.initials,
                      style: TextStyle(
                        color: theme.colorScheme.primaryForeground,
                        fontSize: 12,
                        fontWeight: FontWeight.bold,
                      ),
                    )
                  : null,
            ),
            const SizedBox(width: 8),
            if (AdminResponsiveLayout.isDesktop(context)) ...[
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    adminUser.displayName,
                    style: theme.textTheme.small.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    adminUser.permissionLevel.displayName,
                    style: theme.textTheme.muted.copyWith(
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
              const SizedBox(width: 4),
            ],
            Icon(
              lucide.LucideIcons.chevronDown,
              size: 14,
              color: theme.colorScheme.mutedForeground,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfilePopover(BuildContext context, WidgetRef ref, ShadThemeData theme, adminUser) {
    return ShadCard(
      width: 200,
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Profile info
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  CircleAvatar(
                    radius: 24,
                    backgroundColor: theme.colorScheme.primary,
                    backgroundImage: adminUser.avatarUrl != null 
                        ? NetworkImage(adminUser.avatarUrl!) 
                        : null,
                    child: adminUser.avatarUrl == null
                        ? Text(
                            adminUser.initials,
                            style: TextStyle(
                              color: theme.colorScheme.primaryForeground,
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                        : null,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    adminUser.displayName,
                    style: theme.textTheme.small.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  Text(
                    adminUser.email,
                    style: theme.textTheme.muted.copyWith(
                      fontSize: 11,
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                    decoration: BoxDecoration(
                      color: theme.colorScheme.secondary,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      adminUser.permissionLevel.displayName,
                      style: theme.textTheme.muted.copyWith(
                        fontSize: 10,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            
            const Divider(),
            
            // Menu items
            _buildPopoverMenuItem(
              context,
              theme,
              icon: lucide.LucideIcons.user,
              label: 'Profile',
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Navigate to admin profile page
                LoggerService.info('Admin profile menu item pressed');
              },
            ),
            _buildPopoverMenuItem(
              context,
              theme,
              icon: lucide.LucideIcons.settings,
              label: 'Settings',
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Navigate to admin settings page
                LoggerService.info('Admin settings menu item pressed');
              },
            ),
            _buildPopoverMenuItem(
              context,
              theme,
              icon: lucide.LucideIcons.helpCircle,
              label: 'Help',
              onTap: () {
                Navigator.of(context).pop();
                // TODO: Open help documentation
                LoggerService.info('Admin help menu item pressed');
              },
            ),
            
            const Divider(),
            
            // Sign out
            _buildPopoverMenuItem(
              context,
              theme,
              icon: lucide.LucideIcons.logOut,
              label: 'Sign Out',
              isDestructive: true,
              onTap: () async {
                Navigator.of(context).pop();
                await ref.read(adminAuthProvider.notifier).signOut();
                if (context.mounted) {
                  Navigator.of(context).pushReplacementNamed('/admin/login');
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPopoverMenuItem(
    BuildContext context,
    ShadThemeData theme, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ShadButton.ghost(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 6),
      onPressed: onTap,
      child: Row(
        children: [
          Icon(
            icon,
            size: 16,
            color: isDestructive 
                ? theme.colorScheme.destructive 
                : theme.colorScheme.foreground,
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: theme.textTheme.small.copyWith(
              color: isDestructive 
                  ? theme.colorScheme.destructive 
                  : theme.colorScheme.foreground,
            ),
          ),
        ],
      ),
    );
  }

  IconData _getNavItemIcon(AdminNavigationItem item) {
    switch (item) {
      case AdminNavigationItem.dashboard:
        return lucide.LucideIcons.layoutDashboard;
      case AdminNavigationItem.userManagement:
        return lucide.LucideIcons.users;
      case AdminNavigationItem.contentManagement:
        return lucide.LucideIcons.fileText;
      case AdminNavigationItem.analytics:
        return lucide.LucideIcons.barChart3;
      case AdminNavigationItem.notifications:
        return lucide.LucideIcons.bell;
      case AdminNavigationItem.auditLogs:
        return lucide.LucideIcons.fileSearch;
      case AdminNavigationItem.settings:
        return lucide.LucideIcons.settings;
    }
  }
}
