import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/admin_dashboard_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// Admin sidebar navigation widget for desktop
class AdminSidebarWidget extends ConsumerWidget {
  final AdminNavigationItem selectedItem;
  final Function(AdminNavigationItem) onItemSelected;

  const AdminSidebarWidget({
    super.key,
    required this.selectedItem,
    required this.onItemSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final dashboardState = ref.watch(adminDashboardProvider);
    final availableItems = ref.watch(availableNavItemsProvider);
    final isCollapsed = dashboardState.sidebarCollapsed;

    return Container(
      width: AdminResponsiveLayout.getSidebarWidth(
        context,
        isCollapsed: isCollapsed,
      ),
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          right: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Column(
        children: [
          _buildSidebarHeader(context, ref, theme, isCollapsed),
          Expanded(
            child: _buildNavigationItems(
              context,
              theme,
              availableItems,
              isCollapsed,
            ),
          ),
          _buildSidebarFooter(context, ref, theme, isCollapsed),
        ],
      ),
    );
  }

  Widget _buildSidebarHeader(
    BuildContext context,
    WidgetRef ref,
    ShadThemeData theme,
    bool isCollapsed,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              lucide.LucideIcons.shield,
              size: 20,
              color: theme.colorScheme.primaryForeground,
            ),
          ),
          if (!isCollapsed) ...[
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '3Pay Global',
                    style: theme.textTheme.small.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'Admin Portal',
                    style: theme.textTheme.muted.copyWith(fontSize: 11),
                  ),
                ],
              ),
            ),
          ],
          ShadIconButton.ghost(
            icon: Icon(
              isCollapsed
                  ? lucide.LucideIcons.chevronRight
                  : lucide.LucideIcons.chevronLeft,
              size: 16,
            ),
            onPressed: () {
              ref.read(adminDashboardProvider.notifier).toggleSidebar();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildNavigationItems(
    BuildContext context,
    ShadThemeData theme,
    List<AdminNavigationItem> availableItems,
    bool isCollapsed,
  ) {
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: availableItems.length,
      itemBuilder: (context, index) {
        final item = availableItems[index];
        final isSelected = item == selectedItem;

        return Padding(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
          child: ShadTooltip(
            builder:
                (context) =>
                    isCollapsed ? Text(item.label) : const SizedBox.shrink(),
            child: ShadButton.ghost(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: isCollapsed ? 8 : 12,
                vertical: 12,
              ),
              backgroundColor:
                  isSelected
                      ? theme.colorScheme.primary.withValues(alpha: 0.1)
                      : Colors.transparent,
              onPressed: () {
                onItemSelected(item);
                LoggerService.info('Admin navigation: ${item.label} selected');
              },
              child: Row(
                children: [
                  Icon(
                    _getNavItemIcon(item),
                    size: 18,
                    color:
                        isSelected
                            ? theme.colorScheme.primary
                            : theme.colorScheme.foreground,
                  ),
                  if (!isCollapsed) ...[
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        item.label,
                        style: theme.textTheme.small.copyWith(
                          color:
                              isSelected
                                  ? theme.colorScheme.primary
                                  : theme.colorScheme.foreground,
                          fontWeight:
                              isSelected ? FontWeight.w600 : FontWeight.normal,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSidebarFooter(
    BuildContext context,
    WidgetRef ref,
    ShadThemeData theme,
    bool isCollapsed,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: theme.colorScheme.secondary,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              lucide.LucideIcons.helpCircle,
              size: 16,
              color: theme.colorScheme.secondaryForeground,
            ),
          ),
          if (!isCollapsed) ...[
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Need Help?',
                    style: theme.textTheme.small.copyWith(
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  Text(
                    'Documentation',
                    style: theme.textTheme.muted.copyWith(fontSize: 10),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  IconData _getNavItemIcon(AdminNavigationItem item) {
    switch (item) {
      case AdminNavigationItem.dashboard:
        return lucide.LucideIcons.layoutDashboard;
      case AdminNavigationItem.userManagement:
        return lucide.LucideIcons.users;
      case AdminNavigationItem.contentManagement:
        return lucide.LucideIcons.fileText;
      case AdminNavigationItem.analytics:
        return lucide.LucideIcons.barChart3;
      case AdminNavigationItem.notifications:
        return lucide.LucideIcons.bell;
      case AdminNavigationItem.auditLogs:
        return lucide.LucideIcons.fileSearch;
      case AdminNavigationItem.settings:
        return lucide.LucideIcons.settings;
    }
  }
}

/// Admin bottom navigation widget for mobile
class AdminBottomNavWidget extends ConsumerWidget {
  final AdminNavigationItem selectedItem;
  final Function(AdminNavigationItem) onItemSelected;

  const AdminBottomNavWidget({
    super.key,
    required this.selectedItem,
    required this.onItemSelected,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = ShadTheme.of(context);
    final availableItems = ref.watch(availableNavItemsProvider);

    // Show only the most important items on mobile (max 5)
    final mobileItems = availableItems.take(5).toList();

    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          top: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children:
                mobileItems.map((item) {
                  final isSelected = item == selectedItem;

                  return Expanded(
                    child: ShadButton.ghost(
                      padding: const EdgeInsets.symmetric(vertical: 8),
                      onPressed: () {
                        onItemSelected(item);
                        LoggerService.info(
                          'Admin mobile navigation: ${item.label} selected',
                        );
                      },
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getNavItemIcon(item),
                            size: 20,
                            color:
                                isSelected
                                    ? theme.colorScheme.primary
                                    : theme.colorScheme.mutedForeground,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _getShortLabel(item),
                            style: theme.textTheme.muted.copyWith(
                              fontSize: 10,
                              color:
                                  isSelected
                                      ? theme.colorScheme.primary
                                      : theme.colorScheme.mutedForeground,
                              fontWeight:
                                  isSelected
                                      ? FontWeight.w600
                                      : FontWeight.normal,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ),
    );
  }

  IconData _getNavItemIcon(AdminNavigationItem item) {
    switch (item) {
      case AdminNavigationItem.dashboard:
        return lucide.LucideIcons.layoutDashboard;
      case AdminNavigationItem.userManagement:
        return lucide.LucideIcons.users;
      case AdminNavigationItem.contentManagement:
        return lucide.LucideIcons.fileText;
      case AdminNavigationItem.analytics:
        return lucide.LucideIcons.barChart3;
      case AdminNavigationItem.notifications:
        return lucide.LucideIcons.bell;
      case AdminNavigationItem.auditLogs:
        return lucide.LucideIcons.fileSearch;
      case AdminNavigationItem.settings:
        return lucide.LucideIcons.settings;
    }
  }

  String _getShortLabel(AdminNavigationItem item) {
    switch (item) {
      case AdminNavigationItem.dashboard:
        return 'Dashboard';
      case AdminNavigationItem.userManagement:
        return 'Users';
      case AdminNavigationItem.contentManagement:
        return 'Content';
      case AdminNavigationItem.analytics:
        return 'Analytics';
      case AdminNavigationItem.notifications:
        return 'Alerts';
      case AdminNavigationItem.auditLogs:
        return 'Logs';
      case AdminNavigationItem.settings:
        return 'Settings';
    }
  }
}
