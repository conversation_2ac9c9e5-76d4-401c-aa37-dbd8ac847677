import 'package:flutter/material.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/dashboard_models.dart';

/// KPI Card Widget for dashboard metrics
class KPICard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color? iconColor;
  final TrendIndicator? trend;
  final VoidCallback? onTap;
  final bool isLoading;

  const KPICard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    this.iconColor,
    this.trend,
    this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header row with icon and trend
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: (iconColor ?? theme.colorScheme.primary).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      icon,
                      color: iconColor ?? theme.colorScheme.primary,
                      size: 20,
                    ),
                  ),
                  if (trend != null) _buildTrendIndicator(trend!),
                ],
              ),
              const SizedBox(height: 16),
              
              // Title
              Text(
                title,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              
              // Value
              if (isLoading)
                Container(
                  width: 80,
                  height: 24,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.muted,
                    borderRadius: BorderRadius.circular(4),
                  ),
                )
              else
                Text(
                  value,
                  style: theme.textTheme.h3.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              
              // Subtitle
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: theme.textTheme.muted.copyWith(
                    fontSize: 11,
                  ),
                ),
              ],
              
              // Click indicator
              if (onTap != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      lucide.LucideIcons.externalLink,
                      size: 12,
                      color: theme.colorScheme.mutedForeground,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'View details',
                      style: theme.textTheme.muted.copyWith(
                        fontSize: 10,
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendIndicator(TrendIndicator trend) {
    IconData trendIcon;
    Color trendColor;
    
    switch (trend) {
      case TrendIndicator.up:
        trendIcon = lucide.LucideIcons.trendingUp;
        trendColor = Colors.green;
        break;
      case TrendIndicator.down:
        trendIcon = lucide.LucideIcons.trendingDown;
        trendColor = Colors.red;
        break;
      case TrendIndicator.stable:
        trendIcon = lucide.LucideIcons.minus;
        trendColor = Colors.grey;
        break;
    }
    
    return Container(
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: trendColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(
        trendIcon,
        color: trendColor,
        size: 14,
      ),
    );
  }
}

/// System Health Card Widget
class SystemHealthCard extends StatelessWidget {
  final SystemHealth systemHealth;
  final VoidCallback? onTap;
  final bool isLoading;

  const SystemHealthCard({
    super.key,
    required this.systemHealth,
    this.onTap,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final statusColor = Color(int.parse('0xFF${systemHealth.status.color.substring(1)}'));
    
    return ShadCard(
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      lucide.LucideIcons.activity,
                      color: statusColor,
                      size: 20,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      systemHealth.status.displayName,
                      style: TextStyle(
                        color: statusColor,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              
              // Title
              Text(
                'System Health',
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.mutedForeground,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 4),
              
              // Description
              if (isLoading)
                Container(
                  width: double.infinity,
                  height: 20,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.muted,
                    borderRadius: BorderRadius.circular(4),
                  ),
                )
              else
                Text(
                  systemHealth.description,
                  style: theme.textTheme.small.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
              
              const SizedBox(height: 12),
              
              // Metrics
              if (!isLoading) ...[
                _buildMetricRow('CPU', '${systemHealth.cpuUsage.toStringAsFixed(1)}%', theme),
                const SizedBox(height: 4),
                _buildMetricRow('Memory', '${systemHealth.memoryUsage.toStringAsFixed(1)}%', theme),
                const SizedBox(height: 4),
                _buildMetricRow('Disk', '${systemHealth.diskUsage.toStringAsFixed(1)}%', theme),
              ],
              
              // Click indicator
              if (onTap != null) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(
                      lucide.LucideIcons.externalLink,
                      size: 12,
                      color: theme.colorScheme.mutedForeground,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'View details',
                      style: theme.textTheme.muted.copyWith(
                        fontSize: 10,
                        color: theme.colorScheme.mutedForeground,
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMetricRow(String label, String value, ShadThemeData theme) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: theme.textTheme.muted.copyWith(fontSize: 11),
        ),
        Text(
          value,
          style: theme.textTheme.muted.copyWith(
            fontSize: 11,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }
}

/// Recent Activity Card Widget
class RecentActivityCard extends StatelessWidget {
  final List<RecentActivity> activities;
  final VoidCallback? onViewAll;
  final bool isLoading;

  const RecentActivityCard({
    super.key,
    required this.activities,
    this.onViewAll,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        lucide.LucideIcons.activity,
                        color: theme.colorScheme.primary,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Text(
                      'Recent Activity',
                      style: theme.textTheme.h4.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                if (onViewAll != null)
                  ShadButton.ghost(
                    size: ShadButtonSize.sm,
                    onPressed: onViewAll,
                    child: const Text('View All'),
                  ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Activity list
            if (isLoading)
              Column(
                children: List.generate(3, (index) => _buildLoadingItem(theme)),
              )
            else if (activities.isEmpty)
              Center(
                child: Padding(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    children: [
                      Icon(
                        lucide.LucideIcons.activity,
                        size: 32,
                        color: theme.colorScheme.mutedForeground,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'No recent activity',
                        style: theme.textTheme.muted,
                      ),
                    ],
                  ),
                ),
              )
            else
              Column(
                children: activities.take(5).map((activity) => _buildActivityItem(activity, theme)).toList(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(RecentActivity activity, ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: theme.colorScheme.primary,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  activity.description,
                  style: theme.textTheme.small,
                ),
                const SizedBox(height: 2),
                Text(
                  activity.formattedTimestamp,
                  style: theme.textTheme.muted.copyWith(fontSize: 11),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingItem(ShadThemeData theme) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: theme.colorScheme.muted,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 14,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.muted,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  width: 80,
                  height: 12,
                  decoration: BoxDecoration(
                    color: theme.colorScheme.muted,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
