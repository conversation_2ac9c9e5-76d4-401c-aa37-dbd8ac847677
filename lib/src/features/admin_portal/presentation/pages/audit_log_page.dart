import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_audit_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/audit_log_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/audit_log_widgets.dart';

/// Audit log page for admin portal
class AuditLogPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/audit-logs';

  const AuditLogPage({super.key});

  @override
  ConsumerState<AuditLogPage> createState() => _AuditLogPageState();
}

class _AuditLogPageState extends ConsumerState<AuditLogPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final auditLogState = ref.watch(auditLogProvider);
    final statistics = ref.watch(auditStatisticsProvider);
    final paginationInfo = ref.watch(auditPaginationInfoProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        _buildHeader(theme, auditLogState),
        const SizedBox(height: 24),
        
        // Statistics cards
        if (statistics != null) ...[
          _buildStatisticsCards(theme, statistics),
          const SizedBox(height: 24),
        ],
        
        // Filters section
        _buildFiltersSection(theme, auditLogState),
        const SizedBox(height: 16),
        
        // Error message
        if (auditLogState.error != null) ...[
          _buildErrorCard(theme, auditLogState.error!),
          const SizedBox(height: 16),
        ],
        
        // Export error
        if (auditLogState.exportError != null) ...[
          _buildErrorCard(theme, auditLogState.exportError!),
          const SizedBox(height: 16),
        ],
        
        // Audit logs list
        Expanded(
          child: _buildAuditLogsList(theme, auditLogState),
        ),
        
        // Pagination
        if (auditLogState.hasLogs)
          _buildPagination(theme, paginationInfo),
      ],
    );
  }

  Widget _buildHeader(ShadThemeData theme, AuditLogState state) {
    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Audit Logs',
                style: theme.textTheme.h2,
              ),
              const SizedBox(height: 4),
              Text(
                'Monitor all administrative actions and security events.',
                style: theme.textTheme.muted,
              ),
            ],
          ),
        ),
        
        // Export button
        ShadButton.outline(
          onPressed: state.isExporting ? null : _showExportDialog,
          child: state.isExporting
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(lucide.LucideIcons.download, size: 16),
                    const SizedBox(width: 8),
                    const Text('Export'),
                  ],
                ),
        ),
        const SizedBox(width: 8),
        
        // Refresh button
        ShadIconButton.outline(
          icon: Icon(
            state.isLoading ? lucide.LucideIcons.loader2 : lucide.LucideIcons.refreshCw,
            size: 18,
          ),
          onPressed: state.isLoading ? null : () {
            ref.read(auditLogProvider.notifier).refresh();
          },
        ),
      ],
    );
  }

  Widget _buildStatisticsCards(ShadThemeData theme, AuditStatistics statistics) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 4,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 2.5,
      children: [
        AuditStatisticsCard(
          title: 'Total Entries',
          value: statistics.totalEntries.toString(),
          icon: lucide.LucideIcons.fileText,
          color: Colors.blue,
        ),
        AuditStatisticsCard(
          title: 'Today\'s Entries',
          value: statistics.todayEntries.toString(),
          icon: lucide.LucideIcons.calendar,
          color: Colors.green,
          onTap: () {
            ref.read(auditLogProvider.notifier).loadTodayLogs();
          },
        ),
        AuditStatisticsCard(
          title: 'Security Events',
          value: statistics.securityEvents.toString(),
          icon: lucide.LucideIcons.shield,
          color: Colors.purple,
          onTap: () {
            ref.read(auditLogProvider.notifier).loadSecurityEvents();
          },
        ),
        AuditStatisticsCard(
          title: 'Critical Events',
          value: statistics.criticalEvents.toString(),
          icon: lucide.LucideIcons.alertTriangle,
          color: Colors.red,
          onTap: () {
            ref.read(auditLogProvider.notifier).loadCriticalEvents();
          },
        ),
      ],
    );
  }

  Widget _buildFiltersSection(ShadThemeData theme, AuditLogState state) {
    return Wrap(
      spacing: 16,
      runSpacing: 12,
      children: [
        // Search
        SizedBox(
          width: 300,
          child: ShadInput(
            controller: _searchController,
            placeholder: const Text('Search audit logs...'),
            onChanged: (value) {
              // Debounce search
              Future.delayed(const Duration(milliseconds: 500), () {
                if (_searchController.text == value) {
                  ref.read(auditLogProvider.notifier).updateSearchQuery(value);
                }
              });
            },
          ),
        ),
        
        // Action type filter
        SizedBox(
          width: 180,
          child: ShadSelect<AdminActionType>(
            placeholder: const Text('Action Type'),
            options: [
              const ShadOption(value: null, child: Text('All Types')),
              ...AdminActionType.values.map((type) => ShadOption(
                value: type,
                child: Text(type.displayName),
              )),
            ],
            selectedOptionBuilder: (context, value) => Text(value?.displayName ?? 'All Types'),
            onChanged: (value) {
              ref.read(auditLogProvider.notifier).updateActionTypeFilter(value);
            },
          ),
        ),
        
        // Severity filter
        SizedBox(
          width: 150,
          child: ShadSelect<AdminActionSeverity>(
            placeholder: const Text('Severity'),
            options: [
              const ShadOption(value: null, child: Text('All Severities')),
              ...AdminActionSeverity.values.map((severity) => ShadOption(
                value: severity,
                child: Text(severity.displayName),
              )),
            ],
            selectedOptionBuilder: (context, value) => Text(value?.displayName ?? 'All Severities'),
            onChanged: (value) {
              ref.read(auditLogProvider.notifier).updateSeverityFilter(value);
            },
          ),
        ),
        
        // Quick filters
        ShadButton.outline(
          size: ShadButtonSize.sm,
          onPressed: () {
            ref.read(auditLogProvider.notifier).loadTodayLogs();
          },
          child: const Text('Today'),
        ),
        ShadButton.outline(
          size: ShadButtonSize.sm,
          onPressed: () {
            ref.read(auditLogProvider.notifier).loadSecurityEvents();
          },
          child: const Text('Security'),
        ),
        ShadButton.outline(
          size: ShadButtonSize.sm,
          onPressed: () {
            ref.read(auditLogProvider.notifier).resetFilters();
          },
          child: const Text('Reset'),
        ),
      ],
    );
  }

  Widget _buildErrorCard(ShadThemeData theme, String error) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              lucide.LucideIcons.alertCircle,
              color: theme.colorScheme.destructive,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                error,
                style: TextStyle(color: theme.colorScheme.destructive),
              ),
            ),
            ShadIconButton.ghost(
              icon: const Icon(lucide.LucideIcons.x, size: 16),
              onPressed: () {
                ref.read(auditLogProvider.notifier).clearError();
                ref.read(auditLogProvider.notifier).clearExportError();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuditLogsList(ShadThemeData theme, AuditLogState state) {
    if (state.isLoading) {
      return const AuditLogLoadingWidget();
    }

    if (!state.hasLogs) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              lucide.LucideIcons.fileSearch,
              size: 48,
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            Text(
              'No audit logs found',
              style: theme.textTheme.h4,
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters or search criteria.',
              style: theme.textTheme.muted,
            ),
          ],
        ),
      );
    }

    return ListView.separated(
      padding: const EdgeInsets.all(16),
      itemCount: state.logs.length,
      separatorBuilder: (context, index) => const SizedBox(height: 8),
      itemBuilder: (context, index) {
        final log = state.logs[index];
        return AuditLogEntryWidget(
          entry: log,
          onTap: () => _showAuditLogDetails(log),
        );
      },
    );
  }

  Widget _buildPagination(ShadThemeData theme, Map<String, int> paginationInfo) {
    final currentPage = paginationInfo['currentPage']!;
    final totalPages = paginationInfo['totalPages']!;
    final startItem = paginationInfo['startItem']!;
    final endItem = paginationInfo['endItem']!;
    final totalCount = paginationInfo['totalCount']!;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Text(
            'Showing $startItem-$endItem of $totalCount entries',
            style: theme.textTheme.muted,
          ),
          const Spacer(),
          
          // Previous button
          ShadButton.outline(
            size: ShadButtonSize.sm,
            onPressed: currentPage > 1 ? () {
              ref.read(auditLogProvider.notifier).previousPage();
            } : null,
            child: const Text('Previous'),
          ),
          const SizedBox(width: 8),
          
          // Page info
          Text(
            'Page $currentPage of $totalPages',
            style: theme.textTheme.small,
          ),
          const SizedBox(width: 8),
          
          // Next button
          ShadButton.outline(
            size: ShadButtonSize.sm,
            onPressed: currentPage < totalPages ? () {
              ref.read(auditLogProvider.notifier).nextPage();
            } : null,
            child: const Text('Next'),
          ),
        ],
      ),
    );
  }

  void _showAuditLogDetails(AdminAuditEntry entry) {
    showDialog(
      context: context,
      builder: (context) => AuditLogDetailDialog(entry: entry),
    );
  }

  void _showExportDialog() {
    // TODO: Implement export dialog
    LoggerService.info('Export dialog requested');
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Export functionality will be implemented soon'),
      ),
    );
  }
}
