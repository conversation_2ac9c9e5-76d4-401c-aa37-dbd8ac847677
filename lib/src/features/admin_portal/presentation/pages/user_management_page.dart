import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_user_management_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/user_management_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/user_detail_widget.dart';

/// User management page for admin portal
class UserManagementPage extends ConsumerStatefulWidget {
  const UserManagementPage({super.key});

  @override
  ConsumerState<UserManagementPage> createState() => _UserManagementPageState();
}

class _UserManagementPageState extends ConsumerState<UserManagementPage> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = ShadTheme.of(context);
    final userManagementState = ref.watch(userManagementProvider);
    final paginationInfo = ref.watch(paginationInfoProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        _buildHeader(theme),
        const SizedBox(height: 24),

        // Filters and search
        _buildFiltersSection(theme, userManagementState),
        const SizedBox(height: 16),

        // Bulk actions bar
        if (userManagementState.hasSelection)
          _buildBulkActionsBar(theme, userManagementState),

        // Error message
        if (userManagementState.error != null) ...[
          _buildErrorCard(theme, userManagementState.error!),
          const SizedBox(height: 16),
        ],

        // Users table
        Expanded(child: _buildUsersTable(theme, userManagementState)),

        // Pagination
        if (userManagementState.users.isNotEmpty)
          _buildPagination(theme, paginationInfo),
      ],
    );
  }

  Widget _buildHeader(ShadThemeData theme) {
    final userManagementState = ref.watch(userManagementProvider);

    return Row(
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('User Management', style: theme.textTheme.h2),
              const SizedBox(height: 4),
              Text(
                'Manage all platform users, their status, and permissions.',
                style: theme.textTheme.muted,
              ),
            ],
          ),
        ),

        // Refresh button
        ShadIconButton.outline(
          icon: Icon(
            userManagementState.isLoading
                ? lucide.LucideIcons.loader2
                : lucide.LucideIcons.refreshCw,
            size: 18,
          ),
          onPressed:
              userManagementState.isLoading
                  ? null
                  : () {
                    ref.read(userManagementProvider.notifier).refresh();
                  },
        ),
      ],
    );
  }

  Widget _buildFiltersSection(ShadThemeData theme, UserManagementState state) {
    return Wrap(
      spacing: 16,
      runSpacing: 12,
      children: [
        // Search
        SizedBox(
          width: 300,
          child: ShadInput(
            controller: _searchController,
            placeholder: const Text('Search users...'),
            onChanged: (value) {
              // Debounce search
              Future.delayed(const Duration(milliseconds: 500), () {
                if (_searchController.text == value) {
                  ref
                      .read(userManagementProvider.notifier)
                      .updateSearchQuery(value);
                }
              });
            },
          ),
        ),

        // User type filter
        SizedBox(
          width: 150,
          child: ShadSelect<UserType>(
            placeholder: const Text('User Type'),
            options:
                UserType.values
                    .map(
                      (type) => ShadOption(
                        value: type,
                        child: Text(type.displayName),
                      ),
                    )
                    .toList(),
            selectedOptionBuilder: (context, value) => Text(value.displayName),
            onChanged: (value) {
              if (value != null) {
                ref
                    .read(userManagementProvider.notifier)
                    .updateUserTypeFilter(value);
              }
            },
          ),
        ),

        // Status filter
        SizedBox(
          width: 150,
          child: ShadSelect<UserStatus>(
            placeholder: const Text('Status'),
            options:
                UserStatus.values
                    .map(
                      (status) => ShadOption(
                        value: status,
                        child: Text(status.displayName),
                      ),
                    )
                    .toList(),
            selectedOptionBuilder: (context, value) => Text(value.displayName),
            onChanged: (value) {
              if (value != null) {
                ref
                    .read(userManagementProvider.notifier)
                    .updateStatusFilter(value);
              }
            },
          ),
        ),

        // Sort options
        SizedBox(
          width: 150,
          child: ShadSelect<SortOption>(
            placeholder: const Text('Sort By'),
            options:
                SortOption.values
                    .map(
                      (option) => ShadOption(
                        value: option,
                        child: Text(option.displayName),
                      ),
                    )
                    .toList(),
            selectedOptionBuilder: (context, value) => Text(value.displayName),
            onChanged: (value) {
              if (value != null) {
                ref
                    .read(userManagementProvider.notifier)
                    .updateSortOption(value);
              }
            },
          ),
        ),
      ],
    );
  }

  Widget _buildBulkActionsBar(ShadThemeData theme, UserManagementState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: theme.colorScheme.primary.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: theme.colorScheme.primary.withValues(alpha: 0.2),
        ),
      ),
      child: Row(
        children: [
          Icon(
            lucide.LucideIcons.checkSquare,
            size: 16,
            color: theme.colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            '${state.selectedCount} users selected',
            style: TextStyle(
              color: theme.colorScheme.primary,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 16),

          // Bulk actions
          ShadButton.outline(
            size: ShadButtonSize.sm,
            onPressed: () => _showBulkStatusDialog(UserStatus.active),
            child: const Text('Activate'),
          ),
          const SizedBox(width: 8),
          ShadButton.outline(
            size: ShadButtonSize.sm,
            onPressed: () => _showBulkStatusDialog(UserStatus.suspended),
            child: const Text('Suspend'),
          ),
          const SizedBox(width: 8),
          ShadButton.outline(
            size: ShadButtonSize.sm,
            onPressed: () => _showBulkStatusDialog(UserStatus.deactivated),
            child: const Text('Deactivate'),
          ),

          const Spacer(),

          // Clear selection
          ShadButton.ghost(
            size: ShadButtonSize.sm,
            onPressed: () {
              ref.read(userManagementProvider.notifier).clearSelection();
            },
            child: const Text('Clear Selection'),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorCard(ShadThemeData theme, String error) {
    return ShadCard(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            Icon(
              lucide.LucideIcons.alertCircle,
              color: theme.colorScheme.destructive,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                error,
                style: TextStyle(color: theme.colorScheme.destructive),
              ),
            ),
            ShadIconButton.ghost(
              icon: const Icon(lucide.LucideIcons.x, size: 16),
              onPressed: () {
                ref.read(userManagementProvider.notifier).clearError();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersTable(ShadThemeData theme, UserManagementState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.users.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              lucide.LucideIcons.users,
              size: 48,
              color: theme.colorScheme.mutedForeground,
            ),
            const SizedBox(height: 16),
            Text('No users found', style: theme.textTheme.h4),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your filters or search criteria.',
              style: theme.textTheme.muted,
            ),
          ],
        ),
      );
    }

    return ShadCard(
      child: Column(
        children: [
          // Table header
          _buildTableHeader(theme, state),

          // Table content
          Expanded(
            child: ListView.builder(
              itemCount: state.users.length,
              itemBuilder: (context, index) {
                final user = state.users[index];
                return _buildUserRow(
                  theme,
                  user,
                  state.selectedUserIds.contains(user.id),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader(ShadThemeData theme, UserManagementState state) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.muted.withValues(alpha: 0.3),
        border: Border(bottom: BorderSide(color: theme.colorScheme.border)),
      ),
      child: Row(
        children: [
          // Select all checkbox
          ShadCheckbox(
            value: state.isAllSelected,
            onChanged: (value) {
              if (value == true) {
                ref.read(userManagementProvider.notifier).selectAllUsers();
              } else {
                ref.read(userManagementProvider.notifier).clearSelection();
              }
            },
          ),
          const SizedBox(width: 16),

          // Column headers
          const Expanded(flex: 3, child: Text('User')),
          const Expanded(flex: 2, child: Text('Type')),
          const Expanded(flex: 2, child: Text('Status')),
          const Expanded(flex: 2, child: Text('Created')),
          const Expanded(flex: 2, child: Text('Last Login')),
          const SizedBox(width: 60), // Actions column
        ],
      ),
    );
  }

  Widget _buildUserRow(
    ShadThemeData theme,
    AdminUserModel user,
    bool isSelected,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color:
            isSelected
                ? theme.colorScheme.primary.withValues(alpha: 0.05)
                : null,
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.border.withValues(alpha: 0.5),
          ),
        ),
      ),
      child: Row(
        children: [
          // Checkbox
          ShadCheckbox(
            value: isSelected,
            onChanged: (value) {
              ref
                  .read(userManagementProvider.notifier)
                  .toggleUserSelection(user.id);
            },
          ),
          const SizedBox(width: 16),

          // User info
          Expanded(
            flex: 3,
            child: Row(
              children: [
                CircleAvatar(
                  radius: 16,
                  backgroundColor: theme.colorScheme.primary,
                  backgroundImage:
                      user.avatarUrl != null
                          ? NetworkImage(user.avatarUrl!)
                          : null,
                  child:
                      user.avatarUrl == null
                          ? Text(
                            user.initials,
                            style: TextStyle(
                              color: theme.colorScheme.primaryForeground,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          )
                          : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.displayName,
                        style: theme.textTheme.small.copyWith(
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      Text(
                        user.email,
                        style: theme.textTheme.muted.copyWith(fontSize: 11),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          // User type
          Expanded(
            flex: 2,
            child: Text(
              user.userType.displayName,
              style: theme.textTheme.small,
            ),
          ),

          // Status
          Expanded(flex: 2, child: _buildStatusChip(theme, user.status)),

          // Created date
          Expanded(
            flex: 2,
            child: Text(
              _formatDate(user.created),
              style: theme.textTheme.small,
            ),
          ),

          // Last login
          Expanded(
            flex: 2,
            child: Text(
              user.lastLogin != null ? _formatDate(user.lastLogin!) : 'Never',
              style: theme.textTheme.small,
            ),
          ),

          // Actions
          SizedBox(
            width: 60,
            child: ShadIconButton.ghost(
              icon: const Icon(lucide.LucideIcons.moreHorizontal, size: 16),
              onPressed: () => _showUserDetail(user),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusChip(ShadThemeData theme, UserStatus status) {
    final color = Color(
      int.parse(
        status == UserStatus.active
            ? '0xFF10b981'
            : status == UserStatus.pending
            ? '0xFFf59e0b'
            : '0xFF6b7280',
      ),
    );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Text(
        status.displayName,
        style: TextStyle(
          color: color,
          fontSize: 10,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildPagination(
    ShadThemeData theme,
    Map<String, int> paginationInfo,
  ) {
    final currentPage = paginationInfo['currentPage']!;
    final totalPages = paginationInfo['totalPages']!;
    final startItem = paginationInfo['startItem']!;
    final endItem = paginationInfo['endItem']!;
    final totalCount = paginationInfo['totalCount']!;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Text(
            'Showing $startItem-$endItem of $totalCount users',
            style: theme.textTheme.muted,
          ),
          const Spacer(),

          // Previous button
          ShadButton.outline(
            size: ShadButtonSize.sm,
            onPressed:
                currentPage > 1
                    ? () {
                      ref
                          .read(userManagementProvider.notifier)
                          .goToPage(currentPage - 1);
                    }
                    : null,
            child: const Text('Previous'),
          ),
          const SizedBox(width: 8),

          // Page info
          Text(
            'Page $currentPage of $totalPages',
            style: theme.textTheme.small,
          ),
          const SizedBox(width: 8),

          // Next button
          ShadButton.outline(
            size: ShadButtonSize.sm,
            onPressed:
                currentPage < totalPages
                    ? () {
                      ref
                          .read(userManagementProvider.notifier)
                          .goToPage(currentPage + 1);
                    }
                    : null,
            child: const Text('Next'),
          ),
        ],
      ),
    );
  }

  void _showUserDetail(AdminUserModel user) {
    showDialog(
      context: context,
      builder:
          (context) => UserDetailWidget(
            userId: user.id,
            onUserUpdated: () {
              ref.read(userManagementProvider.notifier).refresh();
            },
          ),
    );
  }

  void _showBulkStatusDialog(UserStatus newStatus) {
    // TODO: Implement bulk status update dialog
    LoggerService.info('Bulk status update to ${newStatus.value} requested');
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
