import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:lucide_icons/lucide_icons.dart' as lucide;
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/analytics_models.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/application/providers/analytics_provider.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/widgets/analytics_chart_widgets.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/utils/admin_responsive_layout.dart';

/// Analytics dashboard page with comprehensive metrics and charts
class AnalyticsDashboardPage extends ConsumerStatefulWidget {
  static const String routeName = '/admin/analytics';

  const AnalyticsDashboardPage({super.key});

  @override
  ConsumerState<AnalyticsDashboardPage> createState() =>
      _AnalyticsDashboardPageState();
}

class _AnalyticsDashboardPageState
    extends ConsumerState<AnalyticsDashboardPage> {
  AnalyticsTimePeriod _selectedPeriod = AnalyticsTimePeriod.last30Days;
  DateTime? _customStartDate;
  DateTime? _customEndDate;

  @override
  Widget build(BuildContext context) {
    final analyticsState = ref.watch(analyticsProvider(_selectedPeriod));
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    final theme = ShadTheme.of(context);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Analytics Dashboard'),
        backgroundColor: theme.colorScheme.background,
        foregroundColor: theme.colorScheme.foreground,
        elevation: 0,
        actions: [
          _buildExportButton(),
          const SizedBox(width: 8),
          _buildRefreshButton(),
          const SizedBox(width: 16),
        ],
      ),
      body: Column(
        children: [
          // Time period selector
          _buildTimePeriodSelector(),

          // Main content
          Expanded(child: _buildContent(analyticsState, isDesktop)),
        ],
      ),
    );
  }

  Widget _buildContent(AnalyticsState state, bool isDesktop) {
    if (state.isLoading) {
      return _buildLoadingState();
    } else if (state.hasError) {
      return _buildErrorState(state.error!);
    } else if (state.hasData) {
      return _buildAnalyticsContent(state.metrics!, isDesktop);
    } else {
      return _buildEmptyState();
    }
  }

  Widget _buildTimePeriodSelector() {
    final theme = ShadTheme.of(context);

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.background,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
      ),
      child: Row(
        children: [
          Text('Time Period:', style: theme.textTheme.small),
          const SizedBox(width: 12),
          Expanded(
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  AnalyticsTimePeriod.values.map((period) {
                    final isSelected = _selectedPeriod == period;
                    return ShadButton.outline(
                      onPressed: () => _onPeriodSelected(period),
                      size: ShadButtonSize.sm,
                      backgroundColor:
                          isSelected
                              ? theme.colorScheme.primary
                              : theme.colorScheme.background,
                      foregroundColor:
                          isSelected
                              ? theme.colorScheme.primaryForeground
                              : theme.colorScheme.foreground,
                      child: Text(period.displayName),
                    );
                  }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportButton() {
    return ShadButton.outline(
      onPressed: _onExportPressed,
      size: ShadButtonSize.sm,
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(lucide.LucideIcons.download, size: 16),
          SizedBox(width: 8),
          Text('Export'),
        ],
      ),
    );
  }

  Widget _buildRefreshButton() {
    return ShadButton.outline(
      onPressed: _onRefreshPressed,
      size: ShadButtonSize.sm,
      child: const Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(lucide.LucideIcons.refreshCw, size: 16),
          SizedBox(width: 8),
          Text('Refresh'),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: 16),
          Text('Loading analytics data...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(String error) {
    final theme = ShadTheme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            lucide.LucideIcons.alertCircle,
            size: 64,
            color: theme.colorScheme.destructive,
          ),
          const SizedBox(height: 16),
          Text('Error loading analytics', style: theme.textTheme.h3),
          const SizedBox(height: 8),
          Text(
            error,
            style: theme.textTheme.muted,
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ShadButton(
            onPressed: _onRefreshPressed,
            child: const Text('Try Again'),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    final theme = ShadTheme.of(context);

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            lucide.LucideIcons.barChart3,
            size: 64,
            color: theme.colorScheme.muted,
          ),
          const SizedBox(height: 16),
          Text('No analytics data available', style: theme.textTheme.h3),
          const SizedBox(height: 8),
          Text(
            'Analytics data will appear here once available',
            style: theme.textTheme.muted,
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsContent(PlatformMetrics metrics, bool isDesktop) {
    return SingleChildScrollView(
      padding: AdminResponsiveLayout.getResponsivePadding(context),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // KPI Summary Cards
          _buildKPISummarySection(metrics),
          const SizedBox(height: 32),

          // Charts Section
          if (isDesktop)
            _buildDesktopChartsLayout(metrics)
          else
            _buildMobileChartsLayout(metrics),

          const SizedBox(height: 32),

          // Additional Metrics Tables
          _buildAdditionalMetricsSection(metrics),
        ],
      ),
    );
  }

  Widget _buildKPISummarySection(PlatformMetrics metrics) {
    final isDesktop = AdminResponsiveLayout.isDesktop(context);
    final crossAxisCount = AdminResponsiveLayout.getCrossAxisCount(
      context,
      mobileCount: 2,
      tabletCount: 3,
      desktopCount: 4,
    );

    final kpis = [
      PlatformKPI.totalUsers,
      PlatformKPI.activeClaims,
      PlatformKPI.fundingVolume,
      PlatformKPI.userGrowthRate,
    ];

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: crossAxisCount,
      crossAxisSpacing: AdminResponsiveLayout.getGridSpacing(context),
      mainAxisSpacing: AdminResponsiveLayout.getGridSpacing(context),
      childAspectRatio: isDesktop ? 1.2 : 1.0,
      children: kpis.map((kpi) => _buildKPICard(kpi, metrics)).toList(),
    );
  }

  Widget _buildKPICard(PlatformKPI kpi, PlatformMetrics metrics) {
    final notifier = ref.read(analyticsProvider(_selectedPeriod).notifier);
    final theme = ShadTheme.of(context);

    return ShadCard(
      child: InkWell(
        onTap: () => _onKPITapped(kpi),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(_getKPIIcon(kpi), color: _getKPIColor(kpi), size: 24),
                  const Spacer(),
                  // TODO: Add trend indicator
                ],
              ),
              const SizedBox(height: 12),
              Text(notifier.getKPIValue(kpi), style: theme.textTheme.h2),
              const SizedBox(height: 4),
              Text(kpi.displayName, style: theme.textTheme.small),
              const SizedBox(height: 2),
              Text(notifier.getKPISubtitle(kpi), style: theme.textTheme.muted),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDesktopChartsLayout(PlatformMetrics metrics) {
    return Column(
      children: [
        // Top row - User and Claim trends
        Row(
          children: [
            Expanded(
              child: AnalyticsChartWidget(
                title: 'User Growth',
                chartType: ChartType.line,
                data: _convertToChartData(
                  metrics.userMetrics.dailyRegistrations,
                ),
                height: 300,
                subtitle: 'Daily user registrations',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AnalyticsChartWidget(
                title: 'Claim Submissions',
                chartType: ChartType.bar,
                data: _convertToChartData(
                  metrics.claimMetrics.dailySubmissions,
                ),
                height: 300,
                subtitle: 'Daily claim submissions',
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),

        // Bottom row - Funding and Distribution
        Row(
          children: [
            Expanded(
              child: AnalyticsChartWidget(
                title: 'Funding Volume',
                chartType: ChartType.area,
                data: _convertDoubleToChartData(
                  metrics.fundingMetrics.dailyVolume,
                ),
                height: 300,
                subtitle: 'Daily funding volume',
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AnalyticsChartWidget(
                title: 'User Type Distribution',
                chartType: ChartType.pie,
                data: _convertMapToChartData(
                  metrics.userMetrics.typeDistribution,
                ),
                height: 300,
                subtitle: 'User distribution by type',
                showLegend: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMobileChartsLayout(PlatformMetrics metrics) {
    return Column(
      children: [
        AnalyticsChartWidget(
          title: 'User Growth',
          chartType: ChartType.line,
          data: _convertToChartData(metrics.userMetrics.dailyRegistrations),
          height: 250,
          subtitle: 'Daily user registrations',
        ),
        const SizedBox(height: 16),
        AnalyticsChartWidget(
          title: 'Claim Submissions',
          chartType: ChartType.bar,
          data: _convertToChartData(metrics.claimMetrics.dailySubmissions),
          height: 250,
          subtitle: 'Daily claim submissions',
        ),
        const SizedBox(height: 16),
        AnalyticsChartWidget(
          title: 'Funding Volume',
          chartType: ChartType.area,
          data: _convertDoubleToChartData(metrics.fundingMetrics.dailyVolume),
          height: 250,
          subtitle: 'Daily funding volume',
        ),
      ],
    );
  }

  Widget _buildAdditionalMetricsSection(PlatformMetrics metrics) {
    final theme = ShadTheme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('Detailed Metrics', style: theme.textTheme.h3),
        const SizedBox(height: 16),
        // TODO: Add detailed metrics tables
        ShadCard(
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Text(
              'Detailed metrics tables will be implemented here',
              style: theme.textTheme.muted,
            ),
          ),
        ),
      ],
    );
  }

  // Helper methods
  List<ChartDataPoint> _convertToChartData(Map<DateTime, int> data) {
    return data.entries
        .map(
          (entry) => ChartDataPoint(
            timestamp: entry.key,
            value: entry.value.toDouble(),
          ),
        )
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  List<ChartDataPoint> _convertDoubleToChartData(Map<DateTime, double> data) {
    return data.entries
        .map(
          (entry) => ChartDataPoint(timestamp: entry.key, value: entry.value),
        )
        .toList()
      ..sort((a, b) => a.timestamp.compareTo(b.timestamp));
  }

  List<ChartDataPoint> _convertMapToChartData(Map<String, int> data) {
    return data.entries
        .map(
          (entry) => ChartDataPoint(
            timestamp: DateTime.now(), // Not used for pie charts
            value: entry.value.toDouble(),
            label: entry.key,
          ),
        )
        .toList();
  }

  IconData _getKPIIcon(PlatformKPI kpi) {
    switch (kpi) {
      case PlatformKPI.totalUsers:
        return lucide.LucideIcons.users;
      case PlatformKPI.activeClaims:
        return lucide.LucideIcons.fileText;
      case PlatformKPI.fundingVolume:
        return lucide.LucideIcons.trendingUp;
      case PlatformKPI.userGrowthRate:
        return lucide.LucideIcons.userPlus;
      default:
        return lucide.LucideIcons.barChart3;
    }
  }

  Color _getKPIColor(PlatformKPI kpi) {
    switch (kpi) {
      case PlatformKPI.totalUsers:
        return Colors.blue;
      case PlatformKPI.activeClaims:
        return Colors.green;
      case PlatformKPI.fundingVolume:
        return Colors.purple;
      case PlatformKPI.userGrowthRate:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  void _onPeriodSelected(AnalyticsTimePeriod period) {
    setState(() {
      _selectedPeriod = period;
    });
  }

  void _onExportPressed() {
    // TODO: Implement export functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Export functionality coming soon')),
    );
  }

  void _onRefreshPressed() {
    ref.read(analyticsProvider(_selectedPeriod).notifier).refreshMetrics();
  }

  void _onKPITapped(PlatformKPI kpi) {
    // TODO: Navigate to detailed KPI view
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Detailed view for ${kpi.displayName} coming soon'),
      ),
    );
  }
}
