import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/admin_user_model.dart';

/// Exception thrown when admin authentication fails
class AdminAuthException implements Exception {
  final String message;
  final String? code;

  const AdminAuthException(this.message, {this.code});

  @override
  String toString() => 'AdminAuthException: $message';
}

/// Service for admin authentication and authorization
class AdminAuthService {
  static final AdminAuthService _instance = AdminAuthService._internal();
  final PocketBaseService _pocketBaseService = PocketBaseService();

  AdminAuthService._internal();

  factory AdminAuthService() => _instance;

  // Delegate to PocketBaseService
  get pb => _pocketBaseService.pb;
  get authStore => _pocketBaseService.authStore;
  get isSignedIn => _pocketBaseService.isSignedIn;
  get currentUser => _pocketBaseService.currentUser;

  Future<dynamic> signIn(String email, String password) =>
      _pocketBaseService.signIn(email, password);
  Future<void> signOut() => _pocketBaseService.signOut();
  Future<dynamic> createRecord({
    required String collectionName,
    required Map<String, dynamic> data,
  }) => _pocketBaseService.createRecord(
    collectionName: collectionName,
    data: data,
  );

  /// Sign in admin user with enhanced security checks
  Future<AdminUser> signInAdmin(String email, String password) async {
    try {
      LoggerService.info('Attempting admin authentication for: $email');

      // Authenticate with PocketBase
      final authData = await signIn(email, password);

      // Verify admin role
      final userType = authData.record?.data['user_type'];
      if (userType != 'admin') {
        LoggerService.warning('Non-admin user attempted admin login: $email');
        await signOut(); // Clear invalid session
        throw const AdminAuthException(
          'Insufficient permissions. Admin access required.',
          code: 'INSUFFICIENT_PERMISSIONS',
        );
      }

      // Check if user account is active
      final isActive = authData.record?.data['active'] ?? true;
      if (!isActive) {
        LoggerService.warning('Inactive admin user attempted login: $email');
        await signOut();
        throw const AdminAuthException(
          'Account is deactivated. Contact system administrator.',
          code: 'ACCOUNT_DEACTIVATED',
        );
      }

      // Create admin user model
      final adminUser = AdminUser.fromRecord(authData.record!);

      // Log successful admin login
      await logAdminActivity('admin_login', {
        'user_id': authData.record!.id,
        'email': email,
        'timestamp': DateTime.now().toIso8601String(),
        'ip_address': 'unknown', // TODO: Get actual IP if needed
        'user_agent': 'flutter_app',
      });

      LoggerService.info('Admin authentication successful for: $email');
      return adminUser;
    } catch (e) {
      LoggerService.error('Admin authentication failed for: $email', e);

      // Log failed login attempt
      await _logFailedLoginAttempt(email, e.toString());

      if (e is AdminAuthException) {
        rethrow;
      }

      // Map PocketBase errors to admin-specific errors
      throw AdminAuthException(_mapAuthError(e), code: 'AUTH_FAILED');
    }
  }

  /// Verify admin permissions for specific actions
  Future<bool> hasPermission(String permission) async {
    try {
      if (!isSignedIn) {
        return false;
      }

      final user = currentUser;
      if (user?.data['user_type'] != 'admin') {
        return false;
      }

      // Get admin permission level
      final permissionLevel = user?.data['permission_level'] ?? 'support_admin';

      // Check permission based on level
      switch (permissionLevel) {
        case 'super_admin':
          return true; // Super admin has all permissions
        case 'content_admin':
          return _contentAdminPermissions.contains(permission);
        case 'support_admin':
          return _supportAdminPermissions.contains(permission);
        default:
          return false;
      }
    } catch (e) {
      LoggerService.error('Error checking admin permission: $permission', e);
      return false;
    }
  }

  /// Log admin activity for audit trail
  Future<void> logAdminActivity(
    String action,
    Map<String, dynamic> details,
  ) async {
    try {
      if (!isSignedIn) return;

      final user = currentUser;
      if (user?.data['user_type'] != 'admin') return;

      await createRecord(
        collectionName: 'user_activity_logs',
        data: {
          'user_id': user!.id,
          'action': action,
          'details': details,
          'timestamp': DateTime.now().toIso8601String(),
          'user_type': 'admin',
          'ip_address': details['ip_address'] ?? 'unknown',
          'user_agent': details['user_agent'] ?? 'flutter_app',
        },
      );

      LoggerService.info('Admin activity logged: $action for user ${user.id}');
    } catch (e) {
      LoggerService.error('Failed to log admin activity: $action', e);
      // Don't throw - logging failure shouldn't break the main operation
    }
  }

  /// Refresh admin session and verify permissions
  Future<void> refreshAdminSession() async {
    try {
      if (!isSignedIn) {
        throw const AdminAuthException(
          'No active session to refresh',
          code: 'NO_SESSION',
        );
      }

      // Verify current user is still admin
      final user = currentUser;
      if (user?.data['user_type'] != 'admin') {
        await signOut();
        throw const AdminAuthException(
          'Admin privileges revoked',
          code: 'PRIVILEGES_REVOKED',
        );
      }

      // Check if account is still active
      final isActive = user?.data['active'] ?? true;
      if (!isActive) {
        await signOut();
        throw const AdminAuthException(
          'Account has been deactivated',
          code: 'ACCOUNT_DEACTIVATED',
        );
      }

      LoggerService.info('Admin session refreshed for user: ${user!.id}');
    } catch (e) {
      LoggerService.error('Failed to refresh admin session', e);
      rethrow;
    }
  }

  /// Get current admin user
  AdminUser? getCurrentAdminUser() {
    try {
      final user = currentUser;
      if (user?.data['user_type'] == 'admin') {
        return AdminUser.fromRecord(user!);
      }
      return null;
    } catch (e) {
      LoggerService.error('Error getting current admin user', e);
      return null;
    }
  }

  /// Log failed login attempt
  Future<void> _logFailedLoginAttempt(String email, String error) async {
    try {
      await createRecord(
        collectionName: 'user_activity_logs',
        data: {
          'action': 'admin_login_failed',
          'details': {
            'email': email,
            'error': error,
            'timestamp': DateTime.now().toIso8601String(),
            'ip_address': 'unknown',
            'user_agent': 'flutter_app',
          },
          'timestamp': DateTime.now().toIso8601String(),
          'user_type': 'admin',
        },
      );
    } catch (e) {
      LoggerService.error('Failed to log failed login attempt', e);
    }
  }

  /// Map authentication errors to user-friendly messages
  String _mapAuthError(dynamic error) {
    final errorString = error.toString().toLowerCase();

    if (errorString.contains('invalid credentials') ||
        errorString.contains('wrong password') ||
        errorString.contains('user not found')) {
      return 'Invalid email or password';
    }

    if (errorString.contains('too many requests') ||
        errorString.contains('rate limit')) {
      return 'Too many login attempts. Please try again later';
    }

    if (errorString.contains('network') || errorString.contains('connection')) {
      return 'Network error. Please check your connection';
    }

    return 'Authentication failed. Please try again';
  }

  /// Content admin permissions
  static const List<String> _contentAdminPermissions = [
    'view_dashboard',
    'manage_content',
    'manage_blog_posts',
    'manage_podcasts',
    'manage_announcements',
    'view_analytics',
    'manage_notifications',
  ];

  /// Support admin permissions
  static const List<String> _supportAdminPermissions = [
    'view_dashboard',
    'view_users',
    'view_analytics',
    'view_audit_logs',
  ];
}
