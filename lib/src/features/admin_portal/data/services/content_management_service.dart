import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/data/models/content_models.dart';

/// Service for managing content creation, editing, and publishing
class ContentManagementService {
  static final ContentManagementService _instance = ContentManagementService._internal();
  final PocketBaseService _pocketBaseService = PocketBaseService();
  
  ContentManagementService._internal();
  
  factory ContentManagementService() => _instance;

  /// Get all content items with filtering options
  Future<List<ContentItem>> getContentItems({
    ContentType? type,
    ContentStatus? status,
    ContentCategory? category,
    String? searchQuery,
    int page = 1,
    int perPage = 20,
  }) async {
    try {
      LoggerService.info('Fetching content items with filters');
      
      String filter = _buildContentFilter(
        type: type,
        status: status,
        category: category,
        searchQuery: searchQuery,
      );
      
      final records = await _pocketBaseService.getList(
        collectionName: 'content_items',
        page: page,
        perPage: perPage,
        filter: filter,
        sort: '-updated',
        expand: 'author_id',
      );
      
      final contentItems = records.items
          .map((record) => _convertRecordToContentItem(record))
          .toList();
      
      LoggerService.info('Fetched ${contentItems.length} content items');
      return contentItems;
    } catch (e) {
      LoggerService.error('Error fetching content items', e);
      rethrow;
    }
  }

  /// Create a new content item
  Future<ContentItem> createContentItem({
    required String title,
    required String content,
    required String authorId,
    required ContentType type,
    String? excerpt,
    ContentCategory category = ContentCategory.news,
    List<String>? tags,
    String? featuredImageId,
    ContentStatus status = ContentStatus.draft,
    Map<String, dynamic>? seoMetadata,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      LoggerService.info('Creating new content item: $title');
      
      final slug = _generateSlug(title);
      final generatedExcerpt = excerpt ?? _generateExcerpt(content);
      
      final data = {
        'title': title,
        'content': content,
        'excerpt': generatedExcerpt,
        'slug': slug,
        'author_id': authorId,
        'content_type': type.value,
        'status': status.value,
        'category': category.value,
        'tags': tags ?? [],
        'featured_image_id': featuredImageId,
        'seo_metadata': {
          'seo_title': title,
          'meta_description': generatedExcerpt,
          'keywords': tags ?? [],
          ...?seoMetadata,
        },
        'metadata': {
          'reading_time': _calculateReadingTime(content),
          'word_count': _countWords(content),
          ...?metadata,
        },
      };
      
      if (status == ContentStatus.published) {
        data['published_at'] = DateTime.now().toIso8601String();
      }
      
      final record = await _pocketBaseService.createRecord(
        collectionName: 'content_items',
        data: data,
      );
      
      // Log content creation
      await _logContentActivity('create_content', {
        'content_id': record.id,
        'title': title,
        'type': type.value,
        'status': status.value,
      });
      
      LoggerService.info('Content item created successfully: ${record.id}');
      return _convertRecordToContentItem(record);
    } catch (e) {
      LoggerService.error('Error creating content item', e);
      rethrow;
    }
  }

  /// Update an existing content item
  Future<ContentItem> updateContentItem({
    required String id,
    String? title,
    String? content,
    String? excerpt,
    ContentCategory? category,
    List<String>? tags,
    String? featuredImageId,
    ContentStatus? status,
    DateTime? scheduledAt,
    Map<String, dynamic>? seoMetadata,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      LoggerService.info('Updating content item: $id');
      
      final updateData = <String, dynamic>{
        'updated': DateTime.now().toIso8601String(),
      };
      
      if (title != null) {
        updateData['title'] = title;
        updateData['slug'] = _generateSlug(title);
      }
      
      if (content != null) {
        updateData['content'] = content;
        updateData['metadata.reading_time'] = _calculateReadingTime(content);
        updateData['metadata.word_count'] = _countWords(content);
        
        if (excerpt == null) {
          updateData['excerpt'] = _generateExcerpt(content);
        }
      }
      
      if (excerpt != null) updateData['excerpt'] = excerpt;
      if (category != null) updateData['category'] = category.value;
      if (tags != null) updateData['tags'] = tags;
      if (featuredImageId != null) updateData['featured_image_id'] = featuredImageId;
      if (scheduledAt != null) updateData['scheduled_at'] = scheduledAt.toIso8601String();
      
      if (status != null) {
        updateData['status'] = status.value;
        if (status == ContentStatus.published) {
          updateData['published_at'] = DateTime.now().toIso8601String();
        }
      }
      
      if (seoMetadata != null) {
        updateData['seo_metadata'] = seoMetadata;
      }
      
      if (metadata != null) {
        updateData['metadata'] = metadata;
      }
      
      final record = await _pocketBaseService.updateRecord(
        collectionName: 'content_items',
        recordId: id,
        data: updateData,
      );
      
      // Log content update
      await _logContentActivity('update_content', {
        'content_id': id,
        'changes': updateData.keys.toList(),
        'status': status?.value,
      });
      
      LoggerService.info('Content item updated successfully: $id');
      return _convertRecordToContentItem(record);
    } catch (e) {
      LoggerService.error('Error updating content item', e);
      rethrow;
    }
  }

  /// Delete a content item
  Future<void> deleteContentItem(String id) async {
    try {
      LoggerService.info('Deleting content item: $id');
      
      await _pocketBaseService.deleteRecord(
        collectionName: 'content_items',
        recordId: id,
      );
      
      // Log content deletion
      await _logContentActivity('delete_content', {
        'content_id': id,
      });
      
      LoggerService.info('Content item deleted successfully: $id');
    } catch (e) {
      LoggerService.error('Error deleting content item', e);
      rethrow;
    }
  }

  /// Duplicate a content item
  Future<ContentItem> duplicateContentItem(String id) async {
    try {
      LoggerService.info('Duplicating content item: $id');
      
      final original = await getContentItem(id);
      
      final duplicatedItem = await createContentItem(
        title: '${original.title} (Copy)',
        content: original.content,
        authorId: original.authorId,
        type: original.type,
        excerpt: original.excerpt,
        category: original.category,
        tags: original.tags,
        featuredImageId: original.featuredImageId,
        status: ContentStatus.draft,
        seoMetadata: original.seoMetadata,
        metadata: original.metadata,
      );
      
      LoggerService.info('Content item duplicated successfully');
      return duplicatedItem;
    } catch (e) {
      LoggerService.error('Error duplicating content item', e);
      rethrow;
    }
  }

  /// Get a single content item by ID
  Future<ContentItem> getContentItem(String id) async {
    try {
      LoggerService.info('Fetching content item: $id');
      
      final record = await _pocketBaseService.getRecord(
        collectionName: 'content_items',
        recordId: id,
        expand: 'author_id',
      );
      
      return _convertRecordToContentItem(record);
    } catch (e) {
      LoggerService.error('Error fetching content item', e);
      rethrow;
    }
  }

  /// Upload media for content
  Future<MediaAsset> uploadContentMedia({
    required File file,
    required String contentId,
    String? altText,
    String? caption,
  }) async {
    try {
      LoggerService.info('Uploading content media for: $contentId');
      
      final multipartFile = await http.MultipartFile.fromPath(
        'file',
        file.path,
      );
      
      final result = await _pocketBaseService.uploadFileAndGetId(
        targetCollectionName: 'content_media',
        multipartFile: multipartFile,
        body: {
          'content_id': contentId,
          'alt_text': altText ?? '',
          'caption': caption ?? '',
          'file_type': _getFileType(file.path),
          'file_size': await file.length(),
          'original_name': file.path.split('/').last,
        },
      );
      
      final mediaRecord = await _pocketBaseService.getRecord(
        collectionName: 'content_media',
        recordId: result['id']!,
      );
      
      LoggerService.info('Media uploaded successfully');
      return _convertRecordToMediaAsset(mediaRecord);
    } catch (e) {
      LoggerService.error('Error uploading content media', e);
      rethrow;
    }
  }

  /// Get media assets for a content item
  Future<List<MediaAsset>> getContentMedia(String contentId) async {
    try {
      LoggerService.info('Fetching media for content: $contentId');
      
      final records = await _pocketBaseService.getFullList(
        collectionName: 'content_media',
        filter: 'content_id = "$contentId"',
        sort: '-created',
      );
      
      final mediaAssets = records
          .map((record) => _convertRecordToMediaAsset(record))
          .toList();
      
      LoggerService.info('Fetched ${mediaAssets.length} media assets');
      return mediaAssets;
    } catch (e) {
      LoggerService.error('Error fetching content media', e);
      rethrow;
    }
  }

  /// Build filter string for content queries
  String _buildContentFilter({
    ContentType? type,
    ContentStatus? status,
    ContentCategory? category,
    String? searchQuery,
  }) {
    final filters = <String>[];
    
    if (type != null) {
      filters.add('content_type = "${type.value}"');
    }
    
    if (status != null) {
      filters.add('status = "${status.value}"');
    }
    
    if (category != null) {
      filters.add('category = "${category.value}"');
    }
    
    if (searchQuery != null && searchQuery.isNotEmpty) {
      filters.add('(title ~ "$searchQuery" || content ~ "$searchQuery" || excerpt ~ "$searchQuery")');
    }
    
    return filters.join(' && ');
  }

  /// Convert PocketBase record to ContentItem
  ContentItem _convertRecordToContentItem(dynamic record) {
    final data = record.data as Map<String, dynamic>;
    
    return ContentItem.fromJson({
      'id': record.id,
      'title': data['title'],
      'content': data['content'],
      'excerpt': data['excerpt'],
      'slug': data['slug'],
      'author_id': data['author_id'],
      'author_name': data['expand']?['author_id']?['name'],
      'content_type': data['content_type'],
      'status': data['status'],
      'category': data['category'],
      'tags': data['tags'],
      'featured_image_id': data['featured_image_id'],
      'created': record.get<String>('created'),
      'updated': record.get<String>('updated'),
      'published_at': data['published_at'],
      'scheduled_at': data['scheduled_at'],
      'seo_metadata': data['seo_metadata'],
      'metadata': data['metadata'],
    });
  }

  /// Convert PocketBase record to MediaAsset
  MediaAsset _convertRecordToMediaAsset(dynamic record) {
    final data = record.data as Map<String, dynamic>;
    
    return MediaAsset.fromJson({
      'id': record.id,
      'file_name': data['file_name'],
      'original_name': data['original_name'],
      'file_type': data['file_type'],
      'file_size': data['file_size'],
      'url': data['url'],
      'thumbnail_url': data['thumbnail_url'],
      'alt_text': data['alt_text'],
      'caption': data['caption'],
      'content_id': data['content_id'],
      'uploaded_at': record.get<String>('created'),
      'metadata': data['metadata'],
    });
  }

  /// Generate URL-friendly slug from title
  String _generateSlug(String title) {
    return title
        .toLowerCase()
        .replaceAll(RegExp(r'[^a-z0-9\s]'), '')
        .replaceAll(RegExp(r'\s+'), '-')
        .trim();
  }

  /// Generate excerpt from content
  String _generateExcerpt(String content, {int maxLength = 160}) {
    final plainText = content.replaceAll(RegExp(r'<[^>]*>'), '');
    return plainText.length > maxLength
        ? '${plainText.substring(0, maxLength)}...'
        : plainText;
  }

  /// Calculate reading time in minutes
  int _calculateReadingTime(String content) {
    final wordCount = _countWords(content);
    return (wordCount / 200).ceil(); // Assuming 200 words per minute
  }

  /// Count words in content
  int _countWords(String content) {
    final plainText = content.replaceAll(RegExp(r'<[^>]*>'), '');
    return plainText.split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
  }

  /// Get file type from file path
  String _getFileType(String filePath) {
    final extension = filePath.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'gif':
        return 'image/gif';
      case 'webp':
        return 'image/webp';
      case 'mp4':
        return 'video/mp4';
      case 'webm':
        return 'video/webm';
      case 'mp3':
        return 'audio/mp3';
      case 'wav':
        return 'audio/wav';
      default:
        return 'application/octet-stream';
    }
  }

  /// Log content management activities
  Future<void> _logContentActivity(String action, Map<String, dynamic> details) async {
    try {
      await _pocketBaseService.createRecord(
        collectionName: 'admin_activity_logs',
        data: {
          'action': action,
          'details': details,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      LoggerService.error('Error logging content activity', e);
      // Don't rethrow as this is not critical
    }
  }
}
