import 'package:pocketbase/pocketbase.dart';

/// Admin permission levels
enum AdminPermissionLevel {
  superAdmin('super_admin', 'Super Admin'),
  contentAdmin('content_admin', 'Content Admin'),
  supportAdmin('support_admin', 'Support Admin');

  const AdminPermissionLevel(this.value, this.displayName);

  final String value;
  final String displayName;

  static AdminPermissionLevel fromString(String value) {
    return AdminPermissionLevel.values.firstWhere(
      (level) => level.value == value,
      orElse: () => AdminPermissionLevel.supportAdmin,
    );
  }
}

/// Admin user model extending the base user with admin-specific fields
class AdminUser {
  final String id;
  final String email;
  final String name;
  final String? firstName;
  final String? lastName;
  final AdminPermissionLevel permissionLevel;
  final DateTime lastLogin;
  final bool mfaEnabled;
  final Map<String, dynamic> preferences;
  final List<String> permissions;
  final bool isActive;
  final DateTime created;
  final DateTime updated;
  final String? avatarUrl;
  final bool verified;

  const AdminUser({
    required this.id,
    required this.email,
    required this.name,
    this.firstName,
    this.lastName,
    required this.permissionLevel,
    required this.lastLogin,
    required this.mfaEnabled,
    required this.preferences,
    required this.permissions,
    required this.isActive,
    required this.created,
    required this.updated,
    this.avatarUrl,
    required this.verified,
  });

  /// Create AdminUser from PocketBase record
  factory AdminUser.fromRecord(RecordModel record) {
    final data = record.data;

    // Parse permission level
    final permissionLevelString =
        data['permission_level'] as String? ?? 'support_admin';
    final permissionLevel = AdminPermissionLevel.fromString(
      permissionLevelString,
    );

    // Parse preferences
    Map<String, dynamic> preferences = {};
    try {
      if (data['preferences'] != null) {
        preferences = Map<String, dynamic>.from(data['preferences'] as Map);
      }
    } catch (e) {
      preferences = {};
    }

    // Parse permissions list
    List<String> permissions = [];
    try {
      if (data['permissions'] != null) {
        permissions = List<String>.from(data['permissions'] as List);
      }
    } catch (e) {
      permissions = [];
    }

    // Parse dates
    final lastLoginString = data['last_login'] as String?;
    final lastLogin =
        lastLoginString != null
            ? DateTime.tryParse(lastLoginString) ?? DateTime.now()
            : DateTime.now();

    final created =
        DateTime.tryParse(record.get<String>('created') ?? '') ??
        DateTime.now();
    final updated =
        DateTime.tryParse(record.get<String>('updated') ?? '') ??
        DateTime.now();

    // Construct avatar URL if present
    String? avatarUrl;
    final avatarFileName = data['avatar'] as String?;
    if (avatarFileName != null && avatarFileName.isNotEmpty) {
      // TODO: Construct proper PocketBase file URL
      avatarUrl = avatarFileName;
    }

    return AdminUser(
      id: record.id,
      email: data['email'] as String? ?? '',
      name: data['name'] as String? ?? '',
      firstName: data['first_name'] as String?,
      lastName: data['last_name'] as String?,
      permissionLevel: permissionLevel,
      lastLogin: lastLogin,
      mfaEnabled: data['mfa_enabled'] as bool? ?? false,
      preferences: preferences,
      permissions: permissions,
      isActive: data['active'] as bool? ?? true,
      created: created,
      updated: updated,
      avatarUrl: avatarUrl,
      verified: data['verified'] as bool? ?? false,
    );
  }

  /// Get display name (first name + last name or fallback to name)
  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    return name.isNotEmpty ? name : email;
  }

  /// Get initials for avatar
  String get initials {
    if (firstName != null && lastName != null) {
      return '${firstName![0]}${lastName![0]}'.toUpperCase();
    }
    if (name.isNotEmpty) {
      final parts = name.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      }
      return name[0].toUpperCase();
    }
    return email[0].toUpperCase();
  }

  /// Check if user has specific permission
  bool hasPermission(String permission) {
    // Super admin has all permissions
    if (permissionLevel == AdminPermissionLevel.superAdmin) {
      return true;
    }

    // Check explicit permissions
    return permissions.contains(permission);
  }

  /// Check if user can manage users
  bool get canManageUsers {
    return permissionLevel == AdminPermissionLevel.superAdmin ||
        hasPermission('manage_users');
  }

  /// Check if user can manage content
  bool get canManageContent {
    return permissionLevel == AdminPermissionLevel.superAdmin ||
        permissionLevel == AdminPermissionLevel.contentAdmin ||
        hasPermission('manage_content');
  }

  /// Check if user can view analytics
  bool get canViewAnalytics {
    return permissionLevel != AdminPermissionLevel.supportAdmin ||
        hasPermission('view_analytics');
  }

  /// Check if user can manage system settings
  bool get canManageSettings {
    return permissionLevel == AdminPermissionLevel.superAdmin ||
        hasPermission('manage_settings');
  }

  /// Get permission level color for UI
  String get permissionLevelColor {
    switch (permissionLevel) {
      case AdminPermissionLevel.superAdmin:
        return '#ef4444'; // Red
      case AdminPermissionLevel.contentAdmin:
        return '#f59e0b'; // Amber
      case AdminPermissionLevel.supportAdmin:
        return '#10b981'; // Emerald
    }
  }

  /// Convert to JSON for updates
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'first_name': firstName,
      'last_name': lastName,
      'permission_level': permissionLevel.value,
      'mfa_enabled': mfaEnabled,
      'preferences': preferences,
      'permissions': permissions,
      'active': isActive,
      'last_login': lastLogin.toIso8601String(),
    };
  }

  /// Create a copy with updated fields
  AdminUser copyWith({
    String? id,
    String? email,
    String? name,
    String? firstName,
    String? lastName,
    AdminPermissionLevel? permissionLevel,
    DateTime? lastLogin,
    bool? mfaEnabled,
    Map<String, dynamic>? preferences,
    List<String>? permissions,
    bool? isActive,
    DateTime? created,
    DateTime? updated,
    String? avatarUrl,
    bool? verified,
  }) {
    return AdminUser(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      permissionLevel: permissionLevel ?? this.permissionLevel,
      lastLogin: lastLogin ?? this.lastLogin,
      mfaEnabled: mfaEnabled ?? this.mfaEnabled,
      preferences: preferences ?? this.preferences,
      permissions: permissions ?? this.permissions,
      isActive: isActive ?? this.isActive,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      verified: verified ?? this.verified,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdminUser && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'AdminUser(id: $id, email: $email, name: $name, permissionLevel: ${permissionLevel.displayName})';
  }
}
