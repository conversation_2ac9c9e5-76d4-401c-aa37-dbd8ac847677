import 'package:pocketbase/pocketbase.dart';

/// User types for filtering
enum UserType {
  all('all', 'All Users'),
  solicitor('solicitor', 'Solicitors'),
  coFunder('co_funder', 'Co-Funders'),
  claimant('claimant', 'Claimants'),
  admin('admin', 'Admins');

  const UserType(this.value, this.displayName);
  
  final String value;
  final String displayName;
  
  static UserType fromString(String value) {
    return UserType.values.firstWhere(
      (type) => type.value == value,
      orElse: () => UserType.all,
    );
  }
}

/// User status for filtering and management
enum UserStatus {
  all('all', 'All Status'),
  active('active', 'Active'),
  inactive('inactive', 'Inactive'),
  pending('pending', 'Pending'),
  suspended('suspended', 'Suspended'),
  deactivated('deactivated', 'Deactivated');

  const UserStatus(this.value, this.displayName);
  
  final String value;
  final String displayName;
  
  static UserStatus fromString(String value) {
    return UserStatus.values.firstWhere(
      (status) => status.value == value,
      orElse: () => UserStatus.active,
    );
  }
}

/// Sort options for user list
enum SortOption {
  nameAsc('name', 'Name (A-Z)'),
  nameDesc('-name', 'Name (Z-A)'),
  dateAsc('created', 'Date (Oldest)'),
  dateDesc('-created', 'Date (Newest)'),
  statusAsc('status', 'Status (A-Z)'),
  statusDesc('-status', 'Status (Z-A)'),
  lastLoginAsc('last_login', 'Last Login (Oldest)'),
  lastLoginDesc('-last_login', 'Last Login (Recent)');

  const SortOption(this.value, this.displayName);
  
  final String value;
  final String displayName;
}

/// Bulk operations for user management
enum BulkUserOperation {
  activate('activate', 'Activate Users'),
  deactivate('deactivate', 'Deactivate Users'),
  suspend('suspend', 'Suspend Users'),
  delete('delete', 'Delete Users'),
  exportData('export', 'Export Data'),
  sendNotification('notify', 'Send Notification');

  const BulkUserOperation(this.value, this.displayName);
  
  final String value;
  final String displayName;
}

/// Unified user model for admin interface
class AdminUserModel {
  final String id;
  final String email;
  final String name;
  final String? firstName;
  final String? lastName;
  final UserType userType;
  final UserStatus status;
  final bool verified;
  final DateTime created;
  final DateTime updated;
  final DateTime? lastLogin;
  final String? avatarUrl;
  final Map<String, dynamic> profileData;
  final String? accessLevel; // For co-funders
  final String? firmName; // For solicitors
  final String? phoneNumber;
  final bool optOut;

  const AdminUserModel({
    required this.id,
    required this.email,
    required this.name,
    this.firstName,
    this.lastName,
    required this.userType,
    required this.status,
    required this.verified,
    required this.created,
    required this.updated,
    this.lastLogin,
    this.avatarUrl,
    required this.profileData,
    this.accessLevel,
    this.firmName,
    this.phoneNumber,
    required this.optOut,
  });

  /// Create AdminUserModel from PocketBase record
  factory AdminUserModel.fromRecord(RecordModel record) {
    final data = record.data;
    
    // Parse user type
    final userTypeString = data['user_type'] as String? ?? 'claimant';
    final userType = UserType.fromString(userTypeString);
    
    // Parse status
    final statusString = data['status'] as String? ?? 'active';
    final status = UserStatus.fromString(statusString);
    
    // Parse dates
    final created = DateTime.tryParse(data['created'] as String? ?? '') ?? DateTime.now();
    final updated = DateTime.tryParse(data['updated'] as String? ?? '') ?? DateTime.now();
    final lastLogin = data['last_login'] != null 
        ? DateTime.tryParse(data['last_login'] as String) 
        : null;
    
    // Extract profile data from expanded relations
    Map<String, dynamic> profileData = {};
    String? accessLevel;
    String? firmName;
    
    // Handle expanded profile data
    final expand = record.expand;
    if (expand != null) {
      switch (userType) {
        case UserType.solicitor:
          final solicitorProfiles = expand['solicitor_profiles'];
          if (solicitorProfiles is List && solicitorProfiles!.isNotEmpty) {
            final profile = solicitorProfiles.first;
            profileData = profile.data;
            firmName = profile.data['firm_name'] as String?;
          }
          break;
        case UserType.coFunder:
          final coFunderProfiles = expand['co_funder_profiles'];
          if (coFunderProfiles is List && coFunderProfiles!.isNotEmpty) {
            final profile = coFunderProfiles.first as RecordModel;
            profileData = profile.data;
            accessLevel = profile.data['access_level'] as String?;
          }
          break;
        case UserType.claimant:
          final claimantProfiles = expand['claimant_profiles'];
          if (claimantProfiles is List && claimantProfiles!.isNotEmpty) {
            final profile = claimantProfiles.first as RecordModel;
            profileData = profile.data;
          }
          break;
        default:
          break;
      }
    }
    
    // Construct avatar URL if present
    String? avatarUrl;
    final avatarFileName = data['avatar'] as String?;
    if (avatarFileName != null && avatarFileName.isNotEmpty) {
      // TODO: Construct proper PocketBase file URL
      avatarUrl = avatarFileName;
    }
    
    return AdminUserModel(
      id: record.id,
      email: data['email'] as String? ?? '',
      name: data['name'] as String? ?? '',
      firstName: data['first_name'] as String?,
      lastName: data['last_name'] as String?,
      userType: userType,
      status: status,
      verified: data['verified'] as bool? ?? false,
      created: created,
      updated: updated,
      lastLogin: lastLogin,
      avatarUrl: avatarUrl,
      profileData: profileData,
      accessLevel: accessLevel,
      firmName: firmName,
      phoneNumber: data['phone_number'] as String?,
      optOut: data['opt_out'] as bool? ?? false,
    );
  }

  /// Get display name
  String get displayName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    }
    return name.isNotEmpty ? name : email;
  }

  /// Get initials for avatar
  String get initials {
    if (firstName != null && lastName != null) {
      return '${firstName![0]}${lastName![0]}'.toUpperCase();
    }
    if (name.isNotEmpty) {
      final parts = name.split(' ');
      if (parts.length >= 2) {
        return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
      }
      return name[0].toUpperCase();
    }
    return email[0].toUpperCase();
  }

  /// Get status color for UI
  String get statusColor {
    switch (status) {
      case UserStatus.active:
        return '#10b981'; // Green
      case UserStatus.inactive:
        return '#6b7280'; // Gray
      case UserStatus.pending:
        return '#f59e0b'; // Amber
      case UserStatus.suspended:
        return '#ef4444'; // Red
      case UserStatus.deactivated:
        return '#8b5cf6'; // Purple
      default:
        return '#6b7280'; // Gray
    }
  }

  /// Get user type icon
  String get userTypeIcon {
    switch (userType) {
      case UserType.solicitor:
        return 'briefcase';
      case UserType.coFunder:
        return 'piggy-bank';
      case UserType.claimant:
        return 'user-check';
      case UserType.admin:
        return 'shield';
      default:
        return 'user';
    }
  }

  /// Check if user can be edited by admin
  bool get canBeEdited {
    return status != UserStatus.deactivated;
  }

  /// Check if user can be deleted
  bool get canBeDeleted {
    return userType != UserType.admin && status != UserStatus.active;
  }

  /// Convert to JSON for updates
  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'first_name': firstName,
      'last_name': lastName,
      'status': status.value,
      'phone_number': phoneNumber,
      'opt_out': optOut,
    };
  }

  /// Create copy with updated fields
  AdminUserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? firstName,
    String? lastName,
    UserType? userType,
    UserStatus? status,
    bool? verified,
    DateTime? created,
    DateTime? updated,
    DateTime? lastLogin,
    String? avatarUrl,
    Map<String, dynamic>? profileData,
    String? accessLevel,
    String? firmName,
    String? phoneNumber,
    bool? optOut,
  }) {
    return AdminUserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      userType: userType ?? this.userType,
      status: status ?? this.status,
      verified: verified ?? this.verified,
      created: created ?? this.created,
      updated: updated ?? this.updated,
      lastLogin: lastLogin ?? this.lastLogin,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      profileData: profileData ?? this.profileData,
      accessLevel: accessLevel ?? this.accessLevel,
      firmName: firmName ?? this.firmName,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      optOut: optOut ?? this.optOut,
    );
  }

  @override
  String toString() {
    return 'AdminUserModel(id: $id, email: $email, name: $name, userType: ${userType.displayName}, status: ${status.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AdminUserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// User filter model for search and filtering
class UserFilter {
  final UserType userType;
  final UserStatus status;
  final String searchQuery;
  final DateTime? startDate;
  final DateTime? endDate;
  final SortOption sortOption;
  final int page;
  final int perPage;

  const UserFilter({
    this.userType = UserType.all,
    this.status = UserStatus.all,
    this.searchQuery = '',
    this.startDate,
    this.endDate,
    this.sortOption = SortOption.dateDesc,
    this.page = 1,
    this.perPage = 50,
  });

  UserFilter copyWith({
    UserType? userType,
    UserStatus? status,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    SortOption? sortOption,
    int? page,
    int? perPage,
  }) {
    return UserFilter(
      userType: userType ?? this.userType,
      status: status ?? this.status,
      searchQuery: searchQuery ?? this.searchQuery,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      sortOption: sortOption ?? this.sortOption,
      page: page ?? this.page,
      perPage: perPage ?? this.perPage,
    );
  }
}

/// Bulk operation result model
class BulkOperationResult {
  final int totalCount;
  final int successCount;
  final int failureCount;
  final List<String> errors;
  final Duration duration;

  const BulkOperationResult({
    required this.totalCount,
    required this.successCount,
    required this.failureCount,
    required this.errors,
    required this.duration,
  });

  bool get isSuccess => failureCount == 0;
  double get successRate => totalCount > 0 ? successCount / totalCount : 0.0;
}
