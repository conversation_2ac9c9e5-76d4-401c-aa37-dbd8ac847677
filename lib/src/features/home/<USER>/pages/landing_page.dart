import 'package:flutter/material.dart';
import 'package:pocketbase/pocketbase.dart'; // Keep if ContentItemModel.fromRecord uses RecordModel
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart'; // Import the ContentItemModel
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart'; // Import the service
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/features/home/<USER>/pages/generic_content_detail_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/auth/presentation/pages/sign_in_page.dart';
import 'dart:async'; // For StreamSubscription
import 'dart:io'; // For SocketException
// Imports for Dashboard Navigation
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/presentation/pages/solicitor_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/presentation/pages/cofunder_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/claimant_portal/presentation/pages/claimant_dashboard_page.dart';
import 'package:three_pay_group_litigation_platform/src/features/admin_portal/presentation/pages/admin_dashboard_page.dart';

class LandingPage extends StatefulWidget {
  const LandingPage({super.key});

  @override
  State<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends State<LandingPage>
    with TickerProviderStateMixin {
  final PocketBase _pb = PocketBaseService().client; // Use the service instance
  List<ContentItemModel> _contentItems = [];
  bool _isLoading = true;
  String? _error;
  bool _isDatabaseConnectionError = false;
  String _selectedContentType =
      'blog'; // Default to 'blog', can be 'podcast', or 'educational_module' for articles
  RecordModel? _currentUser;
  late StreamSubscription<AuthStoreEvent> _authSubscription;

  List<AnimationController> _animationControllers = [];
  List<Animation<double>> _fadeAnimations = [];

  @override
  void initState() {
    super.initState();
    _currentUser = _pb.authStore.record;
    _authSubscription = _pb.authStore.onChange.listen((event) {
      if (mounted) {
        setState(() {
          _currentUser = event.record;
        });
      }
    });
    _fetchLandingPageContent(contentType: _selectedContentType);
  }

  Future<void> _fetchLandingPageContent({required String contentType}) async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _error = null;
      _isDatabaseConnectionError = false;
    });
    try {
      String filter = 'status = "published" && type = "$contentType"';
      // For "articles", we map it to "educational_module" type
      if (contentType == 'articles') {
        filter = 'status = "published" && type = "educational_module"';
      }

      final result = await _pb
          .collection('content_items')
          .getList(
            perPage: 10, // Fetch more items per type
            filter: filter,
            sort: '-published_at',
          );
      final items =
          result.items
              .map((record) => ContentItemModel.fromRecord(record))
              .toList();

      if (!mounted) return;
      setState(() {
        _contentItems = items;
        _isLoading = false;
        _initializeAnimations(); // Initialize animations after data is fetched
      });
    } on SocketException {
      if (!mounted) return;
      setState(() {
        _error =
            'No internet connection. Please check your network and try again.';
        _isDatabaseConnectionError = true;
        _isLoading = false;
      });
    } on ClientException catch (e) {
      if (!mounted) return;
      setState(() {
        // Check for specific database connection errors
        if (e.statusCode == 0 || e.statusCode >= 500) {
          _error =
              'Database service is temporarily unavailable. Please try again later.';
          _isDatabaseConnectionError = true;
        } else if (e.statusCode == 404) {
          _error = 'Content service not found. Please contact support.';
          _isDatabaseConnectionError = true;
        } else {
          _error =
              'Failed to load content: ${e.response['message'] ?? e.toString()}';
          _isDatabaseConnectionError = false;
        }
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        // Check if it's a timeout or connection-related error
        if (e.toString().toLowerCase().contains('timeout') ||
            e.toString().toLowerCase().contains('connection') ||
            e.toString().toLowerCase().contains('network')) {
          _error =
              'Connection timeout. Please check your internet connection and try again.';
          _isDatabaseConnectionError = true;
        } else {
          _error = 'Failed to load content: ${e.toString()}';
          _isDatabaseConnectionError = false;
        }
        _isLoading = false;
      });
    }
  }

  void _initializeAnimations() {
    // Dispose old controllers if any
    for (var controller in _animationControllers) {
      controller.dispose();
    }

    _animationControllers = List.generate(
      _contentItems.length,
      (index) => AnimationController(
        duration: const Duration(milliseconds: 500),
        vsync: this,
      ),
    );

    _fadeAnimations =
        _animationControllers.map((controller) {
          return Tween<double>(
            begin: 0.0,
            end: 1.0,
          ).animate(CurvedAnimation(parent: controller, curve: Curves.easeIn));
        }).toList();

    // Start animations immediately if mounted
    for (int i = 0; i < _animationControllers.length; i++) {
      if (mounted) {
        // The controller would have been created fresh in this same method call,
        // so it shouldn't be disposed yet unless dispose() was called concurrently,
        // which the 'mounted' check helps guard against for the overall state.
        _animationControllers[i].forward();
      }
    }
  }

  @override
  void dispose() {
    for (var controller in _animationControllers) {
      controller.dispose();
    }
    _authSubscription.cancel();
    super.dispose();
  }

  void _onContentTypeSelected(String contentType) {
    if (contentType == 'articles' && _currentUser == null) {
      Navigator.of(context).pushNamed(SignInPage.routeName);
    } else {
      setState(() {
        _selectedContentType = contentType;
      });
      _fetchLandingPageContent(contentType: contentType);
    }
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(context); // Primarily use ShadTheme

    return Scaffold(
      appBar: AppBar(
        title: SizedBox(
          height: kToolbarHeight - 16,
          child: Image.asset('assets/images/3paylogo.png', fit: BoxFit.contain),
        ),
        backgroundColor: shadTheme.colorScheme.border,
        iconTheme: IconThemeData(
          color: shadTheme.colorScheme.primaryForeground,
        ),
      ),
      bottomNavigationBar: BottomAppBar(
        height: 70, // Set a fixed height for the bottom app bar
        color: shadTheme.colorScheme.card,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            _buildBottomNavItem(
              LucideIcons.newspaper,
              'Blog',
              'blog',
              shadTheme,
            ),
            _buildBottomNavItem(
              LucideIcons.podcast,
              'Podcast',
              'podcast',
              shadTheme,
            ),
            _buildDashboardNavItem(shadTheme), // New Dashboard Nav Item
          ],
        ),
      ),
      body: Column(
        // Keep the main Column
        children: [
          // Optional: Add a header indicating the selected content type
          // Padding(
          //   padding: const EdgeInsets.all(16.0),
          //   child: Text(
          //     _selectedContentType == 'articles' ? 'Cofunder Articles' : _selectedContentType[0].toUpperCase() + _selectedContentType.substring(1),
          //     style: shadTheme.textTheme.h3,
          //   ),
          // ),
          Flexible(
            // Use Flexible instead of Expanded for the list
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _error != null
                    ? _buildErrorState(shadTheme)
                    : _contentItems.isEmpty
                    ? Center(
                      child: Text(
                        'No content available.',
                        style: shadTheme.textTheme.p,
                      ),
                    )
                    : LayoutBuilder(
                      builder: (context, constraints) {
                        // Use grid layout for desktop screens (>=1200px)
                        if (constraints.maxWidth >= 1200) {
                          return _buildGridLayout(shadTheme);
                        } else {
                          // Use ListView for mobile/tablet screens
                          return _buildListLayout(shadTheme);
                        }
                      },
                    ),
          ), // Flexible
          // The Padding widget containing the action buttons has been removed.
        ], // children of outer Column
      ), // outer Column
    ); // Scaffold
  }

  // Helper method to check if there's educational content suitable for login prompt
  bool _hasEducationalContentForLogin() {
    return _contentItems.any(
      (item) => item.type == 'blog' || item.type == 'podcast',
    );
  }

  // Build comprehensive error state for database connection issues
  Widget _buildErrorState(ShadThemeData shadTheme) {
    if (_isDatabaseConnectionError) {
      return _buildDatabaseConnectionErrorState(shadTheme);
    } else {
      return _buildGenericErrorState(shadTheme);
    }
  }

  // Build database connection error state with comprehensive UI
  Widget _buildDatabaseConnectionErrorState(ShadThemeData shadTheme) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: ConstrainedBox(
        constraints: BoxConstraints(
          minHeight: MediaQuery.of(context).size.height - 200,
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error illustration - more compact
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: shadTheme.colorScheme.destructive.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(
                  color: shadTheme.colorScheme.destructive.withValues(
                    alpha: 0.2,
                  ),
                  width: 2,
                ),
              ),
              child: Icon(
                LucideIcons.wifiOff,
                size: 48,
                color: shadTheme.colorScheme.destructive,
              ),
            ),
            const SizedBox(height: 24),

            // Error title
            Text(
              'Connection Problem',
              style: shadTheme.textTheme.h2.copyWith(
                fontWeight: FontWeight.bold,
                color: shadTheme.colorScheme.foreground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),

            // Error message - more compact
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: shadTheme.colorScheme.muted.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: shadTheme.colorScheme.border,
                  width: 1,
                ),
              ),
              child: Column(
                children: [
                  Icon(
                    LucideIcons.triangleAlert,
                    size: 20,
                    color: shadTheme.colorScheme.destructive,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _error ?? 'Unable to connect to the database',
                    style: shadTheme.textTheme.small.copyWith(
                      color: shadTheme.colorScheme.foreground,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Troubleshooting steps - more compact
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: shadTheme.colorScheme.card,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: shadTheme.colorScheme.border,
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        LucideIcons.lightbulb,
                        size: 18,
                        color: shadTheme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Troubleshooting Steps',
                        style: shadTheme.textTheme.p.copyWith(
                          fontWeight: FontWeight.w600,
                          color: shadTheme.colorScheme.foreground,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  _buildTroubleshootingStep(
                    shadTheme,
                    LucideIcons.wifi,
                    'Check your internet connection',
                  ),
                  _buildTroubleshootingStep(
                    shadTheme,
                    LucideIcons.refreshCw,
                    'Try refreshing the page',
                  ),
                  _buildTroubleshootingStep(
                    shadTheme,
                    LucideIcons.clock,
                    'Wait a moment and try again',
                  ),
                  _buildTroubleshootingStep(
                    shadTheme,
                    LucideIcons.headphones,
                    'Contact support if the problem persists',
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Action buttons - responsive layout
            Wrap(
              alignment: WrapAlignment.center,
              spacing: 12,
              runSpacing: 12,
              children: [
                ShadButton.outline(
                  onPressed:
                      () => _fetchLandingPageContent(
                        contentType: _selectedContentType,
                      ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        LucideIcons.refreshCw,
                        size: 16,
                        color: shadTheme.colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      const Text('Try Again'),
                    ],
                  ),
                ),
                ShadButton(
                  onPressed: () {
                    // Navigate to offline content or help page
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text('Offline mode not yet available'),
                        backgroundColor: shadTheme.colorScheme.primary,
                      ),
                    );
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        LucideIcons.download,
                        size: 16,
                        color: shadTheme.colorScheme.primaryForeground,
                      ),
                      const SizedBox(width: 8),
                      const Text('Offline Mode'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // Build generic error state for non-connection errors
  Widget _buildGenericErrorState(ShadThemeData shadTheme) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                color: shadTheme.colorScheme.destructive.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Icon(
                LucideIcons.circleAlert,
                size: 48,
                color: shadTheme.colorScheme.destructive,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Something went wrong',
              style: shadTheme.textTheme.h3.copyWith(
                fontWeight: FontWeight.bold,
                color: shadTheme.colorScheme.foreground,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            Text(
              _error ?? 'An unexpected error occurred',
              style: shadTheme.textTheme.p.copyWith(
                color: shadTheme.colorScheme.mutedForeground,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ShadButton(
              onPressed:
                  () => _fetchLandingPageContent(
                    contentType: _selectedContentType,
                  ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    LucideIcons.refreshCw,
                    size: 16,
                    color: shadTheme.colorScheme.primaryForeground,
                  ),
                  const SizedBox(width: 8),
                  const Text('Try Again'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Helper method to build troubleshooting steps
  Widget _buildTroubleshootingStep(
    ShadThemeData shadTheme,
    IconData icon,
    String text,
  ) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: Row(
        children: [
          Icon(icon, size: 14, color: shadTheme.colorScheme.mutedForeground),
          const SizedBox(width: 10),
          Expanded(
            child: Text(
              text,
              style: shadTheme.textTheme.small.copyWith(
                color: shadTheme.colorScheme.mutedForeground,
                height: 1.3,
                fontSize: 12,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build login prompt card for unauthenticated desktop users
  Widget _buildLoginPromptCard(ShadThemeData shadTheme) {
    return ShadCard(
      radius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              shadTheme.colorScheme.primary.withValues(alpha: 0.05),
              shadTheme.colorScheme.secondary.withValues(alpha: 0.05),
            ],
          ),
          border: Border.all(
            color: shadTheme.colorScheme.primary.withValues(alpha: 0.2),
            width: 1,
          ),
        ),
        child: Row(
          children: [
            // Icon section
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: shadTheme.colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                LucideIcons.lock,
                size: 32,
                color: shadTheme.colorScheme.primary,
              ),
            ),
            const SizedBox(width: 20),
            // Content section
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Unlock Educational Content',
                    style: shadTheme.textTheme.h4.copyWith(
                      fontWeight: FontWeight.bold,
                      color: shadTheme.colorScheme.foreground,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Sign in to access our comprehensive library of blogs, podcasts, and educational modules on litigation funding.',
                    style: shadTheme.textTheme.p.copyWith(
                      color: shadTheme.colorScheme.mutedForeground,
                      height: 1.5,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Icon(
                        LucideIcons.bookOpen,
                        size: 16,
                        color: shadTheme.colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Expert insights',
                        style: shadTheme.textTheme.small.copyWith(
                          color: shadTheme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        LucideIcons.headphones,
                        size: 16,
                        color: shadTheme.colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Podcasts',
                        style: shadTheme.textTheme.small.copyWith(
                          color: shadTheme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(width: 16),
                      Icon(
                        LucideIcons.graduationCap,
                        size: 16,
                        color: shadTheme.colorScheme.primary,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        'Learning modules',
                        style: shadTheme.textTheme.small.copyWith(
                          color: shadTheme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(width: 20),
            // Action button
            ShadButton(
              size: ShadButtonSize.lg,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    LucideIcons.logIn,
                    size: 18,
                    color: shadTheme.colorScheme.primaryForeground,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Sign In',
                    style: TextStyle(
                      fontWeight: FontWeight.w600,
                      color: shadTheme.colorScheme.primaryForeground,
                    ),
                  ),
                ],
              ),
              onPressed: () {
                Navigator.of(context).pushNamed(SignInPage.routeName);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBottomNavItem(
    IconData icon,
    String label,
    String contentType,
    ShadThemeData theme,
  ) {
    final bool isActive = _selectedContentType == contentType;
    return Expanded(
      // Ensure items take equal space
      child: ShadButton.ghost(
        padding: EdgeInsets.zero, // Remove padding to save space
        onPressed: () => _onContentTypeSelected(contentType),
        child: SizedBox(
          height: 50, // Fixed height container
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color:
                    isActive
                        ? theme.colorScheme.primary
                        : theme.colorScheme.mutedForeground,
                size: 20, // Even smaller icon
              ),
              const SizedBox(height: 1), // Minimal spacing
              Text(
                label,
                style: theme.textTheme.small.copyWith(
                  color:
                      isActive
                          ? theme.colorScheme.primary
                          : theme.colorScheme.mutedForeground,
                  fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
                  fontSize: 10, // Smaller font size
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDashboardNavItem(ShadThemeData theme) {
    // Determine if a user is logged in to change icon/behavior slightly if desired
    // final bool isLoggedIn = _currentUser != null; // No longer needed for icon/label
    IconData dashboardIcon =
        LucideIcons.layoutDashboard; // Always show dashboard icon
    String dashboardLabel = 'Dashboard'; // Always show 'Dashboard' label

    return Expanded(
      child: ShadButton.ghost(
        padding: EdgeInsets.zero,
        onPressed: () {
          if (_currentUser == null) {
            Navigator.of(context).pushNamed(SignInPage.routeName);
          } else {
            final userType = _currentUser!.data['user_type'];
            switch (userType) {
              case 'solicitor':
                Navigator.of(
                  context,
                ).pushNamed(SolicitorDashboardPage.routeName);
                break;
              case 'co_funder':
                Navigator.of(
                  context,
                ).pushNamed(CoFunderDashboardPage.routeName);
                break;
              case 'claimant':
                Navigator.of(
                  context,
                ).pushNamed(ClaimantDashboardPage.routeName);
                break;
              case 'admin':
                Navigator.of(context).pushNamed(AdminDashboardPage.routeName);
                break;
              default:
                // Fallback to sign-in if user_type is unknown or not set
                Navigator.of(context).pushNamed(SignInPage.routeName);
            }
          }
        },
        child: SizedBox(
          height: 50,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                dashboardIcon,
                color:
                    theme
                        .colorScheme
                        .primary, // Consistently use primary for active/actionable
                size: 20,
              ),
              const SizedBox(height: 1),
              Text(
                dashboardLabel,
                style: theme.textTheme.small.copyWith(
                  color: theme.colorScheme.primary,
                  fontWeight: FontWeight.bold,
                  fontSize: 10,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildGridLayout(ShadThemeData shadTheme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Determine number of columns based on screen width
        int crossAxisCount = 3; // Default 3 columns for iPad
        double childAspectRatio = 1.2; // More horizontal ratio for 3 columns

        if (constraints.maxWidth >= 1200) {
          crossAxisCount = 4; // 4 columns for desktop screens
          childAspectRatio = 1.3; // More horizontal ratio for 4 columns
        }

        if (constraints.maxWidth >= 1600) {
          crossAxisCount = 4; // Keep 4 columns for very wide screens
          childAspectRatio = 1.4; // Even more horizontal for very wide screens
        }

        final screenWidth = constraints.maxWidth;
        final isDesktop = screenWidth >= 1200;
        final isAuthenticated = _currentUser != null;

        // Show login prompt card only on desktop for unauthenticated users
        final shouldShowLoginPrompt =
            isDesktop &&
            !isAuthenticated &&
            _hasEducationalContentForLogin() &&
            (_selectedContentType == 'blog' ||
                _selectedContentType == 'podcast');

        return Padding(
          padding: const EdgeInsets.all(20.0), // Reduced padding for desktop
          child: Column(
            children: [
              // Login prompt card
              if (shouldShowLoginPrompt) ...[
                _buildLoginPromptCard(shadTheme),
                const SizedBox(height: 24),
              ],
              // Content grid
              Expanded(
                child: GridView.builder(
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: crossAxisCount,
                    crossAxisSpacing:
                        16.0, // Reduced horizontal spacing between cards
                    mainAxisSpacing:
                        16.0, // Reduced vertical spacing between cards
                    childAspectRatio:
                        childAspectRatio, // Better proportions for desktop
                  ),
                  itemCount: _contentItems.length,
                  itemBuilder: (context, index) {
                    return _buildContentCard(
                      index,
                      shadTheme,
                      isGridLayout: true,
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildListLayout(ShadThemeData shadTheme) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final screenWidth = constraints.maxWidth;
        final isDesktop = screenWidth >= 1200;
        final isAuthenticated = _currentUser != null;

        // Show login prompt card only on desktop for unauthenticated users
        final shouldShowLoginPrompt =
            isDesktop &&
            !isAuthenticated &&
            _hasEducationalContentForLogin() &&
            (_selectedContentType == 'blog' ||
                _selectedContentType == 'podcast');

        return ListView.builder(
          padding: const EdgeInsets.all(16.0),
          itemCount:
              shouldShowLoginPrompt
                  ? _contentItems.length + 1
                  : _contentItems.length,
          itemBuilder: (context, index) {
            // Show login prompt card as first item if needed
            if (shouldShowLoginPrompt && index == 0) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 24.0),
                child: _buildLoginPromptCard(shadTheme),
              );
            }

            // Adjust index for content items when login prompt is shown
            final contentIndex = shouldShowLoginPrompt ? index - 1 : index;
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              child: _buildContentCard(
                contentIndex,
                shadTheme,
                isGridLayout: false,
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildContentCard(
    int index,
    ShadThemeData shadTheme, {
    bool isGridLayout = false,
  }) {
    final item = _contentItems[index];
    final animation =
        (index < _fadeAnimations.length)
            ? _fadeAnimations[index]
            : const AlwaysStoppedAnimation<double>(1.0);
    final bool hasThumbnail =
        item.thumbnailImage != null && item.thumbnailImage!.isNotEmpty;

    return FadeTransition(
      opacity: animation,
      child: InkWell(
        onTap: () {
          Navigator.pushNamed(
            context,
            GenericContentDetailPage.routeName,
            arguments: item,
          );
        },
        child: ShadCard(
          title: Text(
            item.title,
            style:
                isGridLayout ? shadTheme.textTheme.p : shadTheme.textTheme.h4,
            maxLines: isGridLayout ? 1 : null,
            overflow: isGridLayout ? TextOverflow.ellipsis : null,
          ),
          description: Text(
            item.summary ?? 'No summary available.',
            style:
                isGridLayout
                    ? shadTheme.textTheme.small
                    : shadTheme.textTheme.muted,
            maxLines: isGridLayout ? 1 : 3,
            overflow: TextOverflow.ellipsis,
          ),
          child: Padding(
            padding: EdgeInsets.only(top: isGridLayout ? 2.0 : 8.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height:
                      isGridLayout
                          ? 110
                          : 180, // Optimized image height for grid
                  width: double.infinity,
                  child: ClipRRect(
                    borderRadius: shadTheme.radius,
                    child:
                        hasThumbnail
                            ? Image.network(
                              Uri.parse(
                                '${_pb.baseURL}/api/files/${item.collectionId}/${item.id}/${item.thumbnailImage}',
                              ).toString(),
                              fit: BoxFit.cover,
                              loadingBuilder: (
                                BuildContext context,
                                Widget child,
                                ImageChunkEvent? loadingProgress,
                              ) {
                                if (loadingProgress == null) {
                                  return child;
                                }
                                return Center(
                                  child: CircularProgressIndicator(
                                    value:
                                        loadingProgress.expectedTotalBytes !=
                                                null
                                            ? loadingProgress
                                                    .cumulativeBytesLoaded /
                                                loadingProgress
                                                    .expectedTotalBytes!
                                            : null,
                                  ),
                                );
                              },
                              errorBuilder: (
                                BuildContext context,
                                Object exception,
                                StackTrace? stackTrace,
                              ) {
                                return Container(
                                  color: shadTheme.colorScheme.muted,
                                  alignment: Alignment.center,
                                  child: Icon(
                                    LucideIcons.imageOff,
                                    size: 48,
                                    color:
                                        shadTheme.colorScheme.mutedForeground,
                                  ),
                                );
                              },
                            )
                            : Container(
                              color: shadTheme.colorScheme.muted,
                              alignment: Alignment.center,
                              child: Icon(
                                LucideIcons.image,
                                size: 48,
                                color: shadTheme.colorScheme.mutedForeground,
                              ),
                            ),
                  ),
                ),
                SizedBox(
                  height: isGridLayout ? 12.0 : 12.0,
                ), // Increased spacing for grid
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    ShadBadge.outline(
                      backgroundColor: shadTheme.colorScheme.secondary
                          .withAlpha(26),
                      child: Text(
                        item.type.replaceAll('_', ' ').toUpperCase(),
                        style: shadTheme.textTheme.small.copyWith(
                          color: shadTheme.colorScheme.secondary,
                        ),
                      ),
                    ),
                    if (item.publishedAt != null)
                      Text(
                        DateFormat.yMMMd().format(item.publishedAt!),
                        style: shadTheme.textTheme.small.copyWith(
                          color: shadTheme.colorScheme.mutedForeground,
                        ),
                      ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
} // _LandingPageState
