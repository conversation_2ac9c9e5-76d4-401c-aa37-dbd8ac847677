import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:shadcn_ui/shadcn_ui.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/content_item_model.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_podcast_model.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/background_audio_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/service_locator.dart';
import 'package:flutter_html/flutter_html.dart'; // Import flutter_html
import 'dart:async'; // Import for StreamSubscription

class GenericContentDetailPage extends ConsumerStatefulWidget {
  final ContentItemModel contentItem;

  const GenericContentDetailPage({super.key, required this.contentItem});

  static const String routeName = '/content-detail';

  @override
  ConsumerState<GenericContentDetailPage> createState() =>
      _GenericContentDetailPageState();
}

class _GenericContentDetailPageState
    extends ConsumerState<GenericContentDetailPage> {
  late final BackgroundAudioService _audioService;

  // Corrected getter for podcast URL using media_file
  String? get _podcastUrl {
    final pb = PocketBaseService().client;
    // Check if it's a podcast and has a media_file
    if (widget.contentItem.type == 'podcast' &&
        widget.contentItem.mediaFile != null &&
        widget.contentItem.mediaFile!.isNotEmpty) {
      try {
        // Construct the URL using the PocketBase file structure
        return Uri.parse(
          '${pb.baseUrl}/api/files/${widget.contentItem.collectionId}/${widget.contentItem.id}/${widget.contentItem.mediaFile}',
        ).toString();
      } catch (e) {
        print("Error constructing podcast URL from media_file: $e");
        return null;
      }
    }
    // Fallback or alternative logic if needed (e.g., check media_url if media_file is empty)
    // else if (widget.contentItem.mediaUrl != null && widget.contentItem.mediaUrl!.isNotEmpty && (widget.contentItem.mediaUrl!.startsWith('http://') || widget.contentItem.mediaUrl!.startsWith('https://'))) {
    //    return widget.contentItem.mediaUrl; // Example fallback to media_url if it's a valid external URL
    // }
    return null; // Return null if no valid source found
  }

  @override
  void initState() {
    super.initState();
    _audioService = ServiceLocator.backgroundAudioService;
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> _playPause() async {
    if (!mounted) return;

    final url = _podcastUrl; // Uses the corrected getter
    if (url == null) {
      if (mounted) {
        ShadToaster.of(context).show(
          const ShadToast(
            description: Text('Podcast audio file is not available.'),
          ),
        );
      }
      return;
    }

    try {
      if (!mounted) return;

      // Safely read providers with mounted check
      late final AsyncValue<CoFunderPodcastModel?> currentPodcastAsync;
      late final AsyncValue<AudioPlaybackState> playbackStateAsync;

      try {
        if (!mounted) return;
        currentPodcastAsync = ref.read(currentPodcastStreamProvider);
        playbackStateAsync = ref.read(audioPlaybackStateProvider);
      } catch (e) {
        // Widget was disposed while reading providers
        return;
      }

      final currentPodcast = currentPodcastAsync.valueOrNull;
      final playbackState =
          playbackStateAsync.valueOrNull ?? const AudioPlaybackState();

      // Create a simple podcast model for this content item
      final podcastModel = CoFunderPodcastModel(
        id: widget.contentItem.id,
        collectionId: 'content_items',
        collectionName: 'content_items',
        created: DateTime.now(),
        updated: DateTime.now(),
        contentItemSlug: widget.contentItem.slug,
        episodeNumber: 1, // Default episode number
        playCount: 0,
      );

      // If this content is currently playing, toggle play/pause
      if (currentPodcast?.id == widget.contentItem.id) {
        if (playbackState.isPlaying) {
          await _audioService.pause();
        } else {
          await _audioService.play();
        }
      } else {
        // Start playing this content
        await _audioService.playPodcast(podcastModel, url);
        if (mounted) {
          try {
            ref.read(currentPlayingPodcastProvider.notifier).state =
                podcastModel;
          } catch (e) {
            // Widget was disposed while updating provider
            // This is fine, just ignore
          }
        }
      }
    } catch (e) {
      if (mounted) {
        ShadToaster.of(context).show(
          ShadToast(description: Text('Error playing audio: ${e.toString()}')),
        );
      }
    }
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));
    return '$minutes:$seconds';
  }

  @override
  Widget build(BuildContext context) {
    final shadTheme = ShadTheme.of(context);
    final dateFormat = DateFormat.yMMMd();
    final pb = PocketBaseService().client;
    final imageUrl = widget.contentItem.getImageUrl(pb);
    final bool hasImage = imageUrl != null;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          widget.contentItem.title,
          style: shadTheme.textTheme.h4.copyWith(
            color: shadTheme.colorScheme.primaryForeground,
          ),
        ),
        backgroundColor: shadTheme.colorScheme.primary,
        iconTheme: IconThemeData(
          color: shadTheme.colorScheme.primaryForeground,
        ),
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Author and Date
            Row(
              children: [
                if (widget.contentItem.publishedAt != null)
                  Text(
                    'Published: ${dateFormat.format(widget.contentItem.publishedAt!)}',
                    style: shadTheme.textTheme.small.copyWith(
                      color: shadTheme.colorScheme.mutedForeground,
                    ),
                  ),
              ],
            ),
            const SizedBox(height: 16),

            // Thumbnail Image
            if (hasImage)
              Center(
                child: Hero(
                  tag: 'content_thumbnail_${widget.contentItem.id}',
                  child: ClipRRect(
                    borderRadius: shadTheme.radius,
                    child: Image.network(
                      imageUrl!,
                      height: 250,
                      width: double.infinity,
                      fit: BoxFit.cover,
                      loadingBuilder:
                          (context, child, progress) =>
                              progress == null
                                  ? child
                                  : const Center(
                                    child: CircularProgressIndicator(),
                                  ),
                      errorBuilder:
                          (context, error, stackTrace) => Container(
                            height: 250,
                            color: shadTheme.colorScheme.muted,
                            alignment: Alignment.center,
                            child: Icon(
                              LucideIcons.imageOff,
                              size: 60,
                              color: shadTheme.colorScheme.mutedForeground,
                            ),
                          ),
                    ),
                  ),
                ),
              ),
            if (hasImage) const SizedBox(height: 20),

            // Tags
            if (widget.contentItem.tags.isNotEmpty)
              Wrap(
                spacing: 8.0,
                runSpacing: 4.0,
                children:
                    widget.contentItem.tags
                        .map(
                          (tag) => ShadBadge.outline(
                            child: Text(
                              tag,
                              style: shadTheme.textTheme.small.copyWith(
                                color: shadTheme.colorScheme.secondary,
                              ),
                            ),
                            backgroundColor: shadTheme.colorScheme.secondary
                                .withOpacity(0.1),
                          ),
                        )
                        .toList(),
              ),
            if (widget.contentItem.tags.isNotEmpty) const SizedBox(height: 20),

            // Content-Specific Display
            _buildContentSpecificSection(
              context,
              widget.contentItem,
              shadTheme,
            ),

            const SizedBox(height: 20),
          ],
        ),
      ),
    );
  }

  Widget _buildContentSpecificSection(
    BuildContext context,
    ContentItemModel item,
    ShadThemeData theme,
  ) {
    const textBasedTypes = {
      'blog',
      'newsletter',
      'resource',
      'case_study',
      'article',
      'educational_module',
    };

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Display Body Content using Html widget or Text
        if (textBasedTypes.contains(item.type))
          Html(
            data:
                item.bodyContent ??
                item.summary ??
                '<p>No content available.</p>',
            style: {
              "body": Style(
                fontSize: FontSize(theme.textTheme.p.fontSize ?? 16),
                color: theme.colorScheme.foreground,
                lineHeight: LineHeight.number(1.6),
              ),
              "h1": Style(
                fontSize: FontSize(theme.textTheme.h1.fontSize ?? 32),
                fontWeight: FontWeight.bold,
              ),
              "h2": Style(
                fontSize: FontSize(theme.textTheme.h2.fontSize ?? 28),
                fontWeight: FontWeight.bold,
              ),
              "h3": Style(
                fontSize: FontSize(theme.textTheme.h3.fontSize ?? 24),
                fontWeight: FontWeight.bold,
              ),
              "h4": Style(
                fontSize: FontSize(theme.textTheme.h4.fontSize ?? 20),
                fontWeight: FontWeight.bold,
              ),
              "p": Style(margin: Margins.only(bottom: 16)),
              "a": Style(
                color: theme.colorScheme.primary,
                textDecoration: TextDecoration.none,
              ),
            },
            onLinkTap: (url, _, __) {
              print('Tapped link: $url');
              // TODO: Implement URL launching if needed
            },
          ),

        // Display Podcast specific info with Audio Player
        if (item.type == 'podcast') ...[
          if (item.summary != null && item.summary!.isNotEmpty) ...[
            Text('Summary:', style: theme.textTheme.h4),
            const SizedBox(height: 8),
            SelectableText(item.summary!, style: theme.textTheme.p),
            const SizedBox(height: 16),
          ],
          if (_podcastUrl != null) ...[
            // Check if URL (from media_file) is valid before showing player
            Text('Podcast Episode:', style: theme.textTheme.h4),
            const SizedBox(height: 12),
            Consumer(
              builder: (context, ref, child) {
                final currentPodcastAsync = ref.watch(
                  currentPodcastStreamProvider,
                );
                final playbackStateAsync = ref.watch(
                  audioPlaybackStateProvider,
                );

                return currentPodcastAsync.when(
                  data: (currentPodcast) {
                    return playbackStateAsync.when(
                      data: (playbackState) {
                        // Check if this content is currently playing
                        final isCurrentPodcast =
                            currentPodcast?.id == widget.contentItem.id;
                        final isPlaying =
                            isCurrentPodcast && playbackState.isPlaying;
                        final isLoading =
                            isCurrentPodcast && playbackState.isLoading;
                        final position =
                            isCurrentPodcast
                                ? playbackState.position
                                : Duration.zero;
                        final duration =
                            isCurrentPodcast &&
                                    playbackState.duration.inSeconds > 0
                                ? playbackState.duration
                                : Duration.zero;

                        return Container(
                          // Player UI container
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: theme.colorScheme.muted,
                            borderRadius: theme.radius,
                          ),
                          child: Row(
                            children: [
                              ShadButton.ghost(
                                // Play/Pause button
                                padding: EdgeInsets.zero,
                                onPressed: isLoading ? null : _playPause,
                                child:
                                    isLoading
                                        ? SizedBox(
                                          width: 28,
                                          height: 28,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            valueColor:
                                                AlwaysStoppedAnimation<Color>(
                                                  theme.colorScheme.primary,
                                                ),
                                          ),
                                        )
                                        : Icon(
                                          isPlaying
                                              ? Icons.pause
                                              : Icons.play_arrow,
                                          size: 28,
                                          color: theme.colorScheme.primary,
                                        ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                // Progress Slider
                                child: SliderTheme(
                                  // Customize slider appearance
                                  data: SliderTheme.of(context).copyWith(
                                    trackHeight: 2.0,
                                    thumbShape: const RoundSliderThumbShape(
                                      enabledThumbRadius: 6.0,
                                    ),
                                    overlayShape: const RoundSliderOverlayShape(
                                      overlayRadius: 12.0,
                                    ),
                                    activeTrackColor: theme.colorScheme.primary,
                                    inactiveTrackColor: theme
                                        .colorScheme
                                        .primary
                                        .withValues(alpha: 0.3),
                                    thumbColor: theme.colorScheme.primary,
                                    overlayColor: theme.colorScheme.primary
                                        .withValues(alpha: 0.2),
                                  ),
                                  child: Slider(
                                    min: 0,
                                    max:
                                        duration.inSeconds > 0
                                            ? duration.inSeconds.toDouble()
                                            : 100.0, // Default max when duration unknown
                                    value:
                                        duration.inSeconds > 0
                                            ? position.inSeconds
                                                .toDouble()
                                                .clamp(
                                                  0.0,
                                                  duration.inSeconds.toDouble(),
                                                )
                                            : 0.0, // Default to 0 when duration unknown
                                    onChanged:
                                        duration.inSeconds > 0
                                            ? (value) async {
                                              final newPosition = Duration(
                                                seconds: value.toInt(),
                                              );
                                              await _audioService.seek(
                                                newPosition,
                                              );
                                            }
                                            : null, // Disable when duration unknown
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Text(
                                // Time display
                                duration.inSeconds > 0
                                    ? '${_formatDuration(position)} / ${_formatDuration(duration)}'
                                    : _formatDuration(position),
                                style: theme.textTheme.small.copyWith(
                                  color: theme.colorScheme.mutedForeground,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                      loading:
                          () => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.muted,
                              borderRadius: theme.radius,
                            ),
                            child: const Center(
                              child: CircularProgressIndicator(),
                            ),
                          ),
                      error:
                          (_, __) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                            decoration: BoxDecoration(
                              color: theme.colorScheme.muted,
                              borderRadius: theme.radius,
                            ),
                            child: Text(
                              'Error loading audio player',
                              style: theme.textTheme.p.copyWith(
                                color: theme.colorScheme.destructive,
                              ),
                            ),
                          ),
                    );
                  },
                  loading:
                      () => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.muted,
                          borderRadius: theme.radius,
                        ),
                        child: const Center(child: CircularProgressIndicator()),
                      ),
                  error:
                      (_, __) => Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.muted,
                          borderRadius: theme.radius,
                        ),
                        child: Text(
                          'Error loading audio player',
                          style: theme.textTheme.p.copyWith(
                            color: theme.colorScheme.destructive,
                          ),
                        ),
                      ),
                );
              },
            ),
            const SizedBox(height: 16),
          ] else if (item.type == 'podcast') ...[
            // Show if it's a podcast but no valid media_file was found
            Text('Podcast Episode:', style: theme.textTheme.h4),
            const SizedBox(height: 8),
            Text(
              'Audio file not available for this podcast.',
              style: theme.textTheme.p.copyWith(
                color: theme.colorScheme.destructive,
              ),
            ),
            const SizedBox(height: 16),
          ],
          // Display Show Notes / Transcript (if available)
          if (item.bodyContent != null && item.bodyContent!.isNotEmpty) ...[
            Text('Show Notes / Transcript:', style: theme.textTheme.h4),
            const SizedBox(height: 8),
            Html(
              data: item.bodyContent!,
              style: {
                "body": Style(
                  fontSize: FontSize(theme.textTheme.p.fontSize ?? 16),
                  color: theme.colorScheme.foreground,
                  lineHeight: LineHeight.number(1.6),
                ),
                "p": Style(margin: Margins.only(bottom: 16)),
              },
            ),
          ],
        ],

        // Display Webinar specific info
        if (item.type == 'webinar') ...[
          if (item.summary != null && item.summary!.isNotEmpty) ...[
            Text('Webinar Details:', style: theme.textTheme.h4),
            const SizedBox(height: 8),
            SelectableText(item.summary!, style: theme.textTheme.p),
            const SizedBox(height: 16),
          ],
          if (item.mediaUrl != null && item.mediaUrl!.isNotEmpty) ...[
            // Webinars might still use media_url for external links
            ShadButton(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(LucideIcons.link, size: 16),
                  const SizedBox(width: 8),
                  const Text('Access Webinar'),
                ],
              ),
              onPressed: () {
                ShadToaster.of(context).show(
                  ShadToast(
                    description: Text('Accessing ${item.title} (Placeholder)'),
                  ),
                );
                // TODO: Implement URL launching if needed (requires url_launcher package)
                // _launchUrl(context, item.mediaUrl);
              },
            ),
          ],
        ],
      ],
    );
  }
}
