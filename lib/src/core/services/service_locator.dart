import 'package:three_pay_group_litigation_platform/src/core/services/notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/pocketbase_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/background_audio_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/local_notification_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/logger_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/app_badge_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/google_drive_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/document_cache_service.dart';
import 'package:three_pay_group_litigation_platform/src/features/solicitor_portal/data/services/claim_documents_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/models/storage_type.dart';

// Firebase service import - currently disabled due to Xcode 16/iOS 18.5 compatibility issues
// import 'package:three_pay_group_litigation_platform/src/core/services/firebase_api_service.dart';
import 'package:audio_service/audio_service.dart';

// Simple service locator
class ServiceLocator {
  // Make PocketBaseService instance accessible if needed elsewhere,
  // or ensure it's initialized if it has its own setup.
  static final PocketBaseService pocketBaseService = PocketBaseService();

  // NotificationService depends on PocketBaseService
  static late final NotificationService notificationService;

  // BackgroundAudioService for global audio playback
  static late final BackgroundAudioService backgroundAudioService;

  // GoogleDriveService for Google Drive operations
  static late final GoogleDriveService googleDriveService;

  // DocumentCacheService for intelligent caching
  static late final DocumentCacheService documentCacheService;

  // ClaimDocumentsService with Google Drive integration
  static late final ClaimDocumentsService claimDocumentsService;

  static bool _isInitialized = false;

  static Future<void> initializeAppServices() async {
    if (_isInitialized) return;

    try {
      LoggerService.info('Initializing app services...');

      // Initialize audio service first
      backgroundAudioService = await AudioService.init(
        builder: () => BackgroundAudioService(),
        config: const AudioServiceConfig(
          androidNotificationChannelId: 'com.threepay.global.audio',
          androidNotificationChannelName: '3Pay Global Audio',
          androidNotificationOngoing: true,
          androidStopForegroundOnPause: true,
        ),
      );
      LoggerService.info('Audio service initialized');

      // Initialize background audio service
      await backgroundAudioService.prepare();
      LoggerService.info('Background audio service prepared');

      // Initialize local notification service (already done in main.dart, but ensure it's ready)
      if (!LocalNotificationService.isInitialized) {
        await LocalNotificationService.initialize();
      }
      LoggerService.info('Local notification service ready');

      // Initialize app badge service
      await AppBadgeService.initialize();
      LoggerService.info('App badge service initialized');

      // Initialize Google Drive service
      googleDriveService = GoogleDriveService();
      try {
        await googleDriveService.initialize();
        LoggerService.info('Google Drive service initialized successfully');
      } catch (e) {
        LoggerService.warning(
          'Google Drive service initialization failed (will retry later): $e',
        );
        // Don't fail the entire app initialization if Google Drive fails
      }

      // Initialize Document Cache service
      documentCacheService = DocumentCacheService();
      try {
        await documentCacheService.initialize();
        LoggerService.info('Document Cache service initialized successfully');
      } catch (e) {
        LoggerService.warning(
          'Document Cache service initialization failed (will retry later): $e',
        );
        // Don't fail the entire app initialization if cache fails
      }

      // Initialize Firebase API service (currently disabled)
      // if (!FirebaseApiService.isInitialized()) {
      //   await FirebaseApiService.initNotifications();
      // }
      // LoggerService.info('Firebase API service ready');

      // PocketBaseService is instantiated above.
      // If it had an async init(), it would need to be handled.
      notificationService = NotificationService(pocketBaseService);
      LoggerService.info('Notification service created');

      // Initialize ClaimDocumentsService with Google Drive integration
      claimDocumentsService = ClaimDocumentsService(
        driveService: googleDriveService,
        cacheService: documentCacheService,
        storageConfig: StorageConfiguration.production(),
      );
      LoggerService.info(
        'Claim documents service created with Google Drive integration',
      );

      // Perform health checks
      await _performHealthChecks();

      _isInitialized = true;
      LoggerService.info('App services initialization completed');
      // Note: notificationService.initialize() which starts subscriptions
      // and needs BuildContext for toasts, will be called from AppWidget.
    } catch (e) {
      LoggerService.error('Error initializing app services', e);
      rethrow;
    }
  }

  /// Perform health checks on initialized services
  static Future<void> _performHealthChecks() async {
    try {
      LoggerService.info('Performing service health checks...');

      // Check local notification service
      if (!LocalNotificationService.isInitialized) {
        LoggerService.warning(
          'Local notification service not properly initialized',
        );
      } else {
        LoggerService.info('✓ Local notification service health check passed');
      }

      // Check notification service
      try {
        // Access the service to verify it's initialized
        notificationService.toString();
        LoggerService.info('✓ Notification service health check passed');
      } catch (e) {
        LoggerService.warning('Notification service not initialized: $e');
      }

      // Check PocketBase service
      if (pocketBaseService.pb.authStore.isValid) {
        LoggerService.info('✓ PocketBase service authenticated');
      } else {
        LoggerService.info(
          'PocketBase service not authenticated (expected at startup)',
        );
      }

      // Check background audio service
      try {
        // Access the service to verify it's initialized
        backgroundAudioService.toString();
        LoggerService.info('✓ Background audio service health check passed');
      } catch (e) {
        LoggerService.warning(
          'Background audio service health check failed: $e',
        );
      }

      // Check document cache service
      try {
        // Access the service to verify it's initialized
        documentCacheService.toString();
        LoggerService.info('✓ Document cache service health check passed');
      } catch (e) {
        LoggerService.warning('Document cache service health check failed: $e');
      }

      // Check claim documents service
      try {
        // Access the service to verify it's initialized
        claimDocumentsService.toString();
        LoggerService.info('✓ Claim documents service health check passed');
      } catch (e) {
        LoggerService.warning(
          'Claim documents service health check failed: $e',
        );
      }

      LoggerService.info('Service health checks completed');
    } catch (e) {
      LoggerService.error('Error during service health checks', e);
    }
  }

  /// Get service health status
  static Map<String, bool> getServiceHealthStatus() {
    return {
      'isInitialized': _isInitialized,
      'localNotificationService': LocalNotificationService.isInitialized,
      'notificationService':
          _isInitialized, // Service is available if initialized
      'pocketBaseService': pocketBaseService.pb.authStore.isValid,
      'backgroundAudioService':
          _isInitialized, // Service is available if initialized
      'documentCacheService':
          _isInitialized, // Service is available if initialized
      'claimDocumentsService':
          _isInitialized, // Service is available if initialized
    };
  }

  /// Dispose all services
  static Future<void> disposeServices() async {
    try {
      LoggerService.info('Disposing app services...');

      // Dispose notification service
      if (_isInitialized) {
        notificationService.dispose();
      }

      // Dispose document cache service
      if (_isInitialized) {
        await documentCacheService.dispose();
      }

      // Dispose Google Drive service
      if (_isInitialized) {
        googleDriveService.dispose();
      }

      // Dispose local notification service
      await LocalNotificationService.dispose();

      // Dispose Firebase API service (currently disabled)
      // await FirebaseApiService.dispose();

      _isInitialized = false;
      LoggerService.info('App services disposed successfully');
    } catch (e) {
      LoggerService.error('Error disposing app services', e);
    }
  }
}
