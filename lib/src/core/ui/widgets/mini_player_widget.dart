import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shadcn_ui/shadcn_ui.dart';

import 'package:three_pay_group_litigation_platform/src/core/services/background_audio_service.dart';
import 'package:three_pay_group_litigation_platform/src/core/services/service_locator.dart';
import 'package:three_pay_group_litigation_platform/src/features/cofunder_portal/data/models/cofunder_podcast_model.dart';

class MiniPlayerWidget extends ConsumerWidget {
  const MiniPlayerWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentPodcastAsync = ref.watch(currentPodcastStreamProvider);
    final playbackStateAsync = ref.watch(audioPlaybackStateProvider);

    return currentPodcastAsync.when(
      data: (currentPodcast) {
        // Don't show mini player if no podcast is loaded
        if (currentPodcast == null) {
          return const SizedBox.shrink();
        }

        return playbackStateAsync.when(
          data:
              (playbackState) =>
                  _buildMiniPlayer(context, ref, currentPodcast, playbackState),
          loading:
              () => _buildMiniPlayer(
                context,
                ref,
                currentPodcast,
                const AudioPlaybackState(isLoading: true),
              ),
          error: (_, __) => const SizedBox.shrink(),
        );
      },
      loading: () => const SizedBox.shrink(),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Widget _buildMiniPlayer(
    BuildContext context,
    WidgetRef ref,
    CoFunderPodcastModel currentPodcast,
    AudioPlaybackState playbackState,
  ) {
    final theme = ShadTheme.of(context);
    final audioService = ServiceLocator.backgroundAudioService;

    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: theme.colorScheme.card,
        border: Border(
          top: BorderSide(color: theme.colorScheme.border, width: 1),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Row(
          children: [
            // Podcast info
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    'Episode ${currentPodcast.episodeNumber ?? 'Unknown'}',
                    style: theme.textTheme.small.copyWith(
                      fontWeight: FontWeight.w600,
                      color: theme.colorScheme.foreground,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 2),
                  Text(
                    '3Pay Global Educational Content',
                    style: theme.textTheme.muted.copyWith(
                      color: theme.colorScheme.mutedForeground,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),
                  // Progress bar
                  _buildProgressBar(theme, playbackState),
                ],
              ),
            ),

            const SizedBox(width: 16),

            // Control buttons
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Skip backward button
                ShadButton.ghost(
                  onPressed: () => audioService.skipBackward(),
                  size: ShadButtonSize.sm,
                  child: const Icon(Icons.replay_10, size: 18),
                ),

                const SizedBox(width: 8),

                // Play/pause button
                ShadButton.ghost(
                  onPressed:
                      playbackState.isLoading
                          ? null
                          : () async {
                            if (playbackState.isPlaying) {
                              await audioService.pause();
                            } else {
                              await audioService.play();
                            }
                          },
                  size: ShadButtonSize.sm,
                  child:
                      playbackState.isLoading
                          ? SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                theme.colorScheme.primary,
                              ),
                            ),
                          )
                          : Icon(
                            playbackState.isPlaying
                                ? Icons.pause
                                : Icons.play_arrow,
                            size: 18,
                          ),
                ),

                const SizedBox(width: 8),

                // Skip forward button
                ShadButton.ghost(
                  onPressed: () => audioService.skipForward(),
                  size: ShadButtonSize.sm,
                  child: const Icon(Icons.forward_10, size: 18),
                ),

                const SizedBox(width: 8),

                // Close button
                ShadButton.ghost(
                  onPressed: () async {
                    await audioService.stop();
                    ref.read(currentPlayingPodcastProvider.notifier).state =
                        null;
                  },
                  size: ShadButtonSize.sm,
                  child: const Icon(Icons.close, size: 18),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressBar(
    ShadThemeData theme,
    AudioPlaybackState playbackState,
  ) {
    final progress =
        playbackState.duration.inMilliseconds > 0
            ? playbackState.position.inMilliseconds /
                playbackState.duration.inMilliseconds
            : 0.0;

    return Container(
      height: 3,
      decoration: BoxDecoration(
        color: theme.colorScheme.muted,
        borderRadius: BorderRadius.circular(1.5),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress.clamp(0.0, 1.0),
        child: Container(
          decoration: BoxDecoration(
            color: theme.colorScheme.primary,
            borderRadius: BorderRadius.circular(1.5),
          ),
        ),
      ),
    );
  }
}

// Provider to track if mini player should be shown
final showMiniPlayerProvider = Provider<bool>((ref) {
  final currentPodcast = ref.watch(currentPlayingPodcastProvider);
  return currentPodcast != null;
});

// Widget wrapper that includes mini player
class MiniPlayerWrapper extends ConsumerWidget {
  final Widget child;

  const MiniPlayerWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check if Navigator is available and has routes before showing mini player
    final navigator = Navigator.maybeOf(context);
    final hasRoutes = navigator != null && navigator.canPop();

    final showMiniPlayer = ref.watch(showMiniPlayerProvider) && hasRoutes;

    // Use a safer layout approach that doesn't cause Navigator assertion issues
    // Use a safer layout approach that doesn't cause Navigator assertion issues
    return Stack(
      children: [
        // Main content takes full screen
        Positioned.fill(
          child: child,
        ),
        // Mini player at the bottom - only show if Navigator is available
        if (showMiniPlayer)
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            height: 80, // Explicitly set height
            child: const MiniPlayerWidget(),
          ),
      ],
    );
  }
}
